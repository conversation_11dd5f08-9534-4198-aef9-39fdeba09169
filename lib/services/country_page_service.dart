import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/country_page_model.dart';

class CountryPageService {
  final SupabaseClient _supabase = Supabase.instance.client;

  // Tüm ülke sayfalarını getir
  Future<List<CountryPage>> getAllCountryPages() async {
    try {
      final response = await _supabase
          .from('country_pages')
          .select()
          .order('country', ascending: true);

      return response.map<CountryPage>((json) => CountryPage.fromJson(json)).toList();
    } catch (e) {
      rethrow;
    }
  }

  // Belirli bir ülke sayfasını getir
  Future<CountryPage?> getCountryPageByName(String countryName) async {
    try {
      final response = await _supabase
          .from('country_pages')
          .select()
          .eq('country', countryName)
          .maybeSingle();

      if (response == null) return null;
      
      return CountryPage.fromJson(response);
    } catch (e) {
      rethrow;
    }
  }

  // ID ile ülke sayfasını getir
  Future<CountryPage?> getCountryPageById(String id) async {
    try {
      final response = await _supabase
          .from('country_pages')
          .select()
          .eq('id', id)
          .maybeSingle();

      if (response == null) return null;
      
      return CountryPage.fromJson(response);
    } catch (e) {
      rethrow;
    }
  }

  // Yeni ülke sayfası oluştur (Admin)
  Future<CountryPage> createCountryPage(CountryPage countryPage) async {
    try {
      final response = await _supabase
          .from('country_pages')
          .insert(countryPage.toSupabaseJson())
          .select()
          .single();

      return CountryPage.fromJson(response);
    } catch (e) {
      rethrow;
    }
  }

  // Ülke sayfasını güncelle (Admin)
  Future<CountryPage> updateCountryPage(String id, CountryPage countryPage) async {
    try {
      final updateData = countryPage.toSupabaseJson();
      updateData['updated_at'] = DateTime.now().toIso8601String();

      final response = await _supabase
          .from('country_pages')
          .update(updateData)
          .eq('id', id)
          .select()
          .single();

      return CountryPage.fromJson(response);
    } catch (e) {
      rethrow;
    }
  }

  // Ülke sayfasını sil (Admin)
  Future<void> deleteCountryPage(String id) async {
    try {
      await _supabase
          .from('country_pages')
          .delete()
          .eq('id', id);
    } catch (e) {
      rethrow;
    }
  }

  // Ülke adlarının listesini getir (dropdown için)
  Future<List<String>> getCountryNames() async {
    try {
      final response = await _supabase
          .from('country_pages')
          .select('country')
          .order('country', ascending: true);

      return response.map<String>((item) => item['country'] as String).toList();
    } catch (e) {
      rethrow;
    }
  }

  // Belirli kategorideki bölümleri getir
  Future<List<CountrySection>> getSectionsByCategory(String countryName, String category) async {
    try {
      final countryPage = await getCountryPageByName(countryName);
      if (countryPage == null) return [];

      return countryPage.getSectionsByCategory(category);
    } catch (e) {
      rethrow;
    }
  }

  // Ülke sayfası var mı kontrol et
  Future<bool> countryPageExists(String countryName) async {
    try {
      final response = await _supabase
          .from('country_pages')
          .select('id')
          .eq('country', countryName)
          .maybeSingle();

      return response != null;
    } catch (e) {
      return false;
    }
  }

  // Arama fonksiyonu - ülke adı ve içerikte arama
  Future<List<CountryPage>> searchCountryPages(String query) async {
    try {
      if (query.trim().isEmpty) {
        return getAllCountryPages();
      }

      final response = await _supabase
          .from('country_pages')
          .select()
          .or('country.ilike.%$query%,json_content->>country.ilike.%$query%')
          .order('country', ascending: true);

      return response.map<CountryPage>((json) => CountryPage.fromJson(json)).toList();
    } catch (e) {
      rethrow;
    }
  }

  // Belirli bir ülkenin kategorilerini getir
  Future<List<String>> getCountryCategories(String countryName) async {
    try {
      final countryPage = await getCountryPageByName(countryName);
      if (countryPage == null) return [];

      return countryPage.categories;
    } catch (e) {
      rethrow;
    }
  }

  // Tüm kategorileri getir (tüm ülkelerden)
  Future<List<String>> getAllCategories() async {
    try {
      final countryPages = await getAllCountryPages();
      final Set<String> allCategories = {};

      for (final page in countryPages) {
        allCategories.addAll(page.categories);
      }

      return allCategories.toList()..sort();
    } catch (e) {
      rethrow;
    }
  }

  // Son güncellenen ülke sayfalarını getir
  Future<List<CountryPage>> getRecentlyUpdatedPages({int limit = 5}) async {
    try {
      final response = await _supabase
          .from('country_pages')
          .select()
          .order('updated_at', ascending: false)
          .limit(limit);

      return response.map<CountryPage>((json) => CountryPage.fromJson(json)).toList();
    } catch (e) {
      rethrow;
    }
  }

  // Ülke sayfası istatistikleri
  Future<Map<String, dynamic>> getCountryPageStats() async {
    try {
      final countryPages = await getAllCountryPages();
      
      int totalSections = 0;
      int totalLinks = 0;
      final Set<String> allCategories = {};

      for (final page in countryPages) {
        totalSections += page.sections.length;
        allCategories.addAll(page.categories);
        
        for (final section in page.sections) {
          totalLinks += section.links.length;
        }
      }

      return {
        'totalCountries': countryPages.length,
        'totalSections': totalSections,
        'totalLinks': totalLinks,
        'totalCategories': allCategories.length,
        'categories': allCategories.toList()..sort(),
      };
    } catch (e) {
      rethrow;
    }
  }
}
