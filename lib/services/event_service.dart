import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/event_model.dart';
import '../models/user_model.dart';

class EventService {
  final SupabaseClient _supabase = Supabase.instance.client;

  // Get all events
  Future<List<Event>> getAllEvents() async {
    try {
      final response = await _supabase
          .from('events')
          .select()
          .order('date', ascending: true);

      return response.map<Event>((json) => Event.fromJson(json)).toList();
    } catch (e) {
      rethrow;
    }
  }

  // Get events by city
  Future<List<Event>> getEventsByCity(String city) async {
    try {
      final response = await _supabase
          .from('events')
          .select()
          .eq('city', city)
          .order('date', ascending: true);

      return response.map<Event>((json) => Event.fromJson(json)).toList();
    } catch (e) {
      rethrow;
    }
  }

  // Create a new event
  Future<void> createEvent({
    required String title,
    required String description,
    required String city,
    required DateTime date,
    File? imageFile,
  }) async {
    try {
      String? imageUrl;

      // Upload image if provided
      if (imageFile != null) {
        final fileName = '${DateTime.now().millisecondsSinceEpoch}.jpg';
        await _supabase.storage.from('event-images').upload(
              fileName,
              imageFile,
              fileOptions: const FileOptions(cacheControl: '3600', upsert: false),
            );

        imageUrl = _supabase.storage.from('event-images').getPublicUrl(fileName);
      }

      // Create event
      await _supabase.from('events').insert({
        'title': title,
        'description': description,
        'city': city,
        'date': date.toIso8601String(),
        'image_url': imageUrl,
        'participants': [],
      });
    } catch (e) {
      rethrow;
    }
  }

  // Update an event
  Future<void> updateEvent({
    required String id,
    required String title,
    required String description,
    required String city,
    required DateTime date,
    File? imageFile,
  }) async {
    try {
      final eventData = {
        'title': title,
        'description': description,
        'city': city,
        'date': date.toIso8601String(),
      };

      // Upload new image if provided
      if (imageFile != null) {
        final fileName = '${DateTime.now().millisecondsSinceEpoch}.jpg';
        await _supabase.storage.from('event-images').upload(
              fileName,
              imageFile,
              fileOptions: const FileOptions(cacheControl: '3600', upsert: false),
            );

        eventData['image_url'] = _supabase.storage.from('event-images').getPublicUrl(fileName);
      }

      // Update event
      await _supabase.from('events').update(eventData).eq('id', id);
    } catch (e) {
      rethrow;
    }
  }

  // Delete an event
  Future<void> deleteEvent(String id) async {
    try {
      await _supabase.from('events').delete().eq('id', id);
    } catch (e) {
      rethrow;
    }
  }

  // Join an event
  Future<void> joinEvent(String eventId, String userId) async {
    try {
      // Get current event
      final event = await _supabase
          .from('events')
          .select()
          .eq('id', eventId)
          .single();

      // Get current participants
      List<String> participants = List<String>.from(event['participants'] ?? []);

      // Add user if not already participating
      if (!participants.contains(userId)) {
        participants.add(userId);

        // Update event with new participants list
        await _supabase
            .from('events')
            .update({'participants': participants})
            .eq('id', eventId);
      }
    } catch (e) {
      rethrow;
    }
  }

  // Leave an event
  Future<void> leaveEvent(String eventId, String userId) async {
    try {
      // Get current event
      final event = await _supabase
          .from('events')
          .select()
          .eq('id', eventId)
          .single();

      // Get current participants
      List<String> participants = List<String>.from(event['participants'] ?? []);

      // Remove user if participating
      if (participants.contains(userId)) {
        participants.remove(userId);

        // Update event with new participants list
        await _supabase
            .from('events')
            .update({'participants': participants})
            .eq('id', eventId);
      }
    } catch (e) {
      rethrow;
    }
  }

  // Get event participants
  Future<List<AppUser>> getEventParticipants(String eventId) async {
    try {
      // Get current event
      final event = await _supabase
          .from('events')
          .select()
          .eq('id', eventId)
          .single();

      // Get participant IDs
      List<String> participantIds = List<String>.from(event['participants'] ?? []);

      if (participantIds.isEmpty) {
        return [];
      }

      // Get user data for all participants
      final usersData = await _supabase
          .from('users')
          .select()
          .in_('id', participantIds);

      // Get admin data to check which participants are admins
      final adminsData = await _supabase
          .from('admins')
          .select('user_id');

      final adminIds = adminsData.map<String>((admin) => admin['user_id'] as String).toList();

      // Convert to AppUser objects
      return usersData.map<AppUser>((userData) {
        final isAdmin = adminIds.contains(userData['id']);
        return AppUser.fromJson(userData, isAdmin: isAdmin);
      }).toList();
    } catch (e) {
      rethrow;
    }
  }
}
