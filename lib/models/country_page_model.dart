class CountrySection {
  final String title;
  final String category;
  final String icon;
  final String? imageUrl;
  final String content;
  final List<CountryLink> links;
  final String? videoUrl;
  final List<String> tags;

  CountrySection({
    required this.title,
    required this.category,
    required this.icon,
    this.imageUrl,
    required this.content,
    required this.links,
    this.videoUrl,
    required this.tags,
  });

  // JSON'dan CountrySection oluştur
  factory CountrySection.fromJson(Map<String, dynamic> json) {
    return CountrySection(
      title: json['title'] as String,
      category: json['category'] as String,
      icon: json['icon'] as String,
      imageUrl: json['imageUrl'] as String?,
      content: json['content'] as String,
      links: (json['links'] as List<dynamic>?)
              ?.map((link) => CountryLink.fromJson(link as Map<String, dynamic>))
              .toList() ??
          [],
      videoUrl: json['videoUrl'] as String?,
      tags: List<String>.from(json['tags'] as List<dynamic>? ?? []),
    );
  }

  // CountrySection'ı JSON'a çevir
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'category': category,
      'icon': icon,
      'imageUrl': imageUrl,
      'content': content,
      'links': links.map((link) => link.toJson()).toList(),
      'videoUrl': videoUrl,
      'tags': tags,
    };
  }
}

class CountryLink {
  final String label;
  final String url;

  CountryLink({
    required this.label,
    required this.url,
  });

  // JSON'dan CountryLink oluştur
  factory CountryLink.fromJson(Map<String, dynamic> json) {
    return CountryLink(
      label: json['label'] as String,
      url: json['url'] as String,
    );
  }

  // CountryLink'i JSON'a çevir
  Map<String, dynamic> toJson() {
    return {
      'label': label,
      'url': url,
    };
  }
}

class CountryPage {
  final String id;
  final String country;
  final List<CountrySection> sections;
  final DateTime createdAt;
  final DateTime updatedAt;

  CountryPage({
    required this.id,
    required this.country,
    required this.sections,
    required this.createdAt,
    required this.updatedAt,
  });

  // Supabase JSON'ından CountryPage oluştur
  factory CountryPage.fromJson(Map<String, dynamic> json) {
    final jsonContent = json['json_content'] as Map<String, dynamic>;
    
    return CountryPage(
      id: json['id'] as String,
      country: json['country'] as String,
      sections: (jsonContent['sections'] as List<dynamic>?)
              ?.map((section) => CountrySection.fromJson(section as Map<String, dynamic>))
              .toList() ??
          [],
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  // CountryPage'i Supabase JSON formatına çevir
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'country': country,
      'json_content': {
        'country': country,
        'sections': sections.map((section) => section.toJson()).toList(),
      },
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Sadece JSON içeriğini al (Supabase insert/update için)
  Map<String, dynamic> toSupabaseJson() {
    return {
      'country': country,
      'json_content': {
        'country': country,
        'sections': sections.map((section) => section.toJson()).toList(),
      },
      'updated_at': DateTime.now().toIso8601String(),
    };
  }

  // Kategoriye göre bölümleri grupla
  Map<String, List<CountrySection>> get sectionsByCategory {
    final Map<String, List<CountrySection>> grouped = {};
    
    for (final section in sections) {
      if (!grouped.containsKey(section.category)) {
        grouped[section.category] = [];
      }
      grouped[section.category]!.add(section);
    }
    
    return grouped;
  }

  // Belirli bir kategorideki bölümleri al
  List<CountrySection> getSectionsByCategory(String category) {
    return sections.where((section) => section.category == category).toList();
  }

  // Tüm kategorileri al
  List<String> get categories {
    return sections.map((section) => section.category).toSet().toList();
  }

  // Kopya oluştur (güncelleme için)
  CountryPage copyWith({
    String? id,
    String? country,
    List<CountrySection>? sections,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CountryPage(
      id: id ?? this.id,
      country: country ?? this.country,
      sections: sections ?? this.sections,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
