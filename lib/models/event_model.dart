import 'package:intl/intl.dart';

class Event {
  final String id;
  final String title;
  final String description;
  final String city;
  final DateTime date;
  final String imageUrl;
  final List<String> participants;

  Event({
    required this.id,
    required this.title,
    required this.description,
    required this.city,
    required this.date,
    required this.imageUrl,
    required this.participants,
  });

  // Convert Supabase JSON response to Event object
  factory Event.fromJson(Map<String, dynamic> json) {
    return Event(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      city: json['city'] as String,
      date: DateTime.parse(json['date'] as String),
      imageUrl: json['image_url'] as String? ?? '',
      participants: List<String>.from(json['participants'] as List? ?? []),
    );
  }

  // Convert Event object to JSON for Supabase
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'city': city,
      'date': date.toIso8601String(),
      'image_url': imageUrl,
      'participants': participants,
    };
  }

  // Format date for display
  String get formattedDate {
    return DateFormat('dd MMMM yyyy, HH:mm', 'tr_TR').format(date);
  }

  // Check if a user is participating in this event
  bool isParticipating(String userId) {
    return participants.contains(userId);
  }

  // Get number of participants
  int get participantCount {
    return participants.length;
  }
}
