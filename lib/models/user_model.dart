class AppUser {
  final String id;
  final String email;
  final String city;
  final String country;
  final DateTime createdAt;
  final bool isAdmin;

  AppUser({
    required this.id,
    required this.email,
    required this.city,
    required this.country,
    required this.createdAt,
    this.isAdmin = false,
  });

  // Convert Supabase JSON response to AppUser object
  factory AppUser.fromJson(Map<String, dynamic> json, {bool isAdmin = false}) {
    return AppUser(
      id: json['id'] as String,
      email: json['email'] as String,
      city: json['city'] as String? ?? '',
      country: json['country'] as String? ?? 'Türkiye',
      createdAt: DateTime.parse(json['created_at'] as String),
      isAdmin: isAdmin,
    );
  }

  // Convert AppUser object to JSON for Supabase
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'city': city,
      'country': country,
      'created_at': createdAt.toIso8601String(),
    };
  }
}
