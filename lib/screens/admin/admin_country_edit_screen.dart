import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../../models/user_model.dart';
import '../../models/country_page_model.dart';
import '../../services/auth_service.dart';
import '../../services/country_page_service.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/responsive_container.dart';

class AdminCountryEditScreen extends StatefulWidget {
  final CountryPage? countryPage; // null ise yeni ülke ekleme

  const AdminCountryEditScreen({
    Key? key,
    this.countryPage,
  }) : super(key: key);

  @override
  State<AdminCountryEditScreen> createState() => _AdminCountryEditScreenState();
}

class _AdminCountryEditScreenState extends State<AdminCountryEditScreen> {
  final AuthService _authService = AuthService();
  final CountryPageService _countryPageService = CountryPageService();
  
  AppUser? _currentUser;
  bool _isLoading = true;
  bool _isSaving = false;
  
  final _formKey = GlobalKey<FormState>();
  final _countryController = TextEditingController();
  
  List<CountrySection> _sections = [];

  bool get _isEditing => widget.countryPage != null;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _countryController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Kullanıcı bilgilerini yükle
      _currentUser = await _authService.getCurrentUserData();
      
      // Admin kontrolü
      if (_currentUser?.isAdmin != true) {
        if (mounted) {
          Navigator.of(context).pop();
          Fluttertoast.showToast(
            msg: "Bu sayfaya erişim yetkiniz yok",
            toastLength: Toast.LENGTH_LONG,
            gravity: ToastGravity.BOTTOM,
          );
        }
        return;
      }

      // Düzenleme modundaysa mevcut verileri yükle
      if (_isEditing) {
        _countryController.text = widget.countryPage!.country;
        _sections = List.from(widget.countryPage!.sections);
      } else {
        // Yeni ülke için boş bir bölüm ekle
        _addNewSection();
      }
    } catch (e) {
      if (mounted) {
        Fluttertoast.showToast(
          msg: "Veriler yüklenirken hata oluştu: $e",
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _addNewSection() {
    setState(() {
      _sections.add(
        CountrySection(
          title: '',
          category: 'Genel Bilgi',
          icon: '🏡',
          content: '',
          links: [],
          tags: [],
        ),
      );
    });
  }

  void _removeSection(int index) {
    setState(() {
      _sections.removeAt(index);
    });
  }

  Future<void> _saveCountryPage() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_sections.isEmpty) {
      Fluttertoast.showToast(
        msg: "En az bir bölüm eklemelisiniz",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
      );
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final countryPage = CountryPage(
        id: _isEditing ? widget.countryPage!.id : '',
        country: _countryController.text.trim(),
        sections: _sections,
        createdAt: _isEditing ? widget.countryPage!.createdAt : DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (_isEditing) {
        await _countryPageService.updateCountryPage(
          widget.countryPage!.id,
          countryPage,
        );
        if (mounted) {
          Fluttertoast.showToast(
            msg: "Ülke bilgileri güncellendi",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM,
          );
        }
      } else {
        await _countryPageService.createCountryPage(countryPage);
        if (mounted) {
          Fluttertoast.showToast(
            msg: "Yeni ülke eklendi",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM,
          );
        }
      }

      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        Fluttertoast.showToast(
          msg: "Kaydetme işlemi başarısız: $e",
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: _isEditing ? 'Ülke Düzenle' : 'Yeni Ülke Ekle',
        currentUser: _currentUser,
        actions: [
          if (!_isLoading)
            TextButton(
              onPressed: _isSaving ? null : _saveCountryPage,
              child: _isSaving
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Kaydet'),
            ),
        ],
      ),
      body: ResponsiveContainer(
        maxWidth: 800,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _buildContent(),
      ),
    );
  }

  Widget _buildContent() {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Ülke adı
            _buildCountryNameField(),
            
            const SizedBox(height: 24),
            
            // Bölümler başlığı
            _buildSectionsHeader(),
            
            const SizedBox(height: 16),
            
            // Bölümler listesi
            ..._buildSectionsList(),
            
            const SizedBox(height: 24),
            
            // Kaydet butonu
            _buildSaveButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildCountryNameField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Ülke Adı',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _countryController,
          decoration: InputDecoration(
            hintText: 'Örn: Hollanda, Almanya, Fransa',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            filled: true,
            fillColor: Colors.grey[50],
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Ülke adı gereklidir';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildSectionsHeader() {
    return Row(
      children: [
        const Text(
          'Bölümler',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        ElevatedButton.icon(
          onPressed: _addNewSection,
          icon: const Icon(Icons.add, size: 18),
          label: const Text('Bölüm Ekle'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
        ),
      ],
    );
  }

  List<Widget> _buildSectionsList() {
    return _sections.asMap().entries.map((entry) {
      final index = entry.key;
      final section = entry.value;
      
      return Container(
        margin: const EdgeInsets.only(bottom: 16),
        child: _buildSectionCard(index, section),
      );
    }).toList();
  }

  Widget _buildSectionCard(int index, CountrySection section) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Bölüm başlığı ve sil butonu
            Row(
              children: [
                Text(
                  'Bölüm ${index + 1}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (_sections.length > 1)
                  IconButton(
                    onPressed: () => _removeSection(index),
                    icon: const Icon(Icons.delete, color: Colors.red),
                    tooltip: 'Bölümü Sil',
                  ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Basit form alanları (detaylı editör için ayrı ekran gerekebilir)
            Text(
              'Bu bölüm için detaylı düzenleme özelliği yakında eklenecek.\n'
              'Şu an için JSON editörü kullanabilirsiniz.',
              style: TextStyle(
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
            
            const SizedBox(height: 8),
            
            // Mevcut bölüm bilgileri (sadece görüntüleme)
            if (section.title.isNotEmpty) ...[
              Text('Başlık: ${section.title}'),
              Text('Kategori: ${section.category}'),
              Text('İkon: ${section.icon}'),
              if (section.content.isNotEmpty)
                Text('İçerik: ${section.content.substring(0, section.content.length > 100 ? 100 : section.content.length)}...'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isSaving ? null : _saveCountryPage,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: _isSaving
            ? const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  SizedBox(width: 8),
                  Text('Kaydediliyor...'),
                ],
              )
            : Text(_isEditing ? 'Güncelle' : 'Kaydet'),
      ),
    );
  }
}
