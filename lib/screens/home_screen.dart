import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/responsive_container.dart';
import '../widgets/home_cards/login_register_card.dart';
import '../widgets/home_cards/about_app_card.dart';
import '../widgets/home_cards/events_login_card.dart';
import '../widgets/home_cards/create_event_card.dart';
import '../widgets/home_cards/discover_events_card.dart';
import '../widgets/home_cards/my_events_card.dart';
import '../widgets/home_cards/communities_card.dart';
import '../widgets/home_cards/profile_card.dart';
import '../widgets/home_cards/country_guide_card.dart';
import 'event_list_screen.dart';
import 'profile_screen.dart';
import 'admin/admin_panel_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final AuthService _authService = AuthService();
  AppUser? _currentUser;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    // Hata ayıklama mesajları
    debugPrint('HomeScreen: initState çağrıldı');
    debugPrint('HomeScreen: Kullanıcı giriş yapmış mı: ${_authService.isLoggedIn}');

    // Kullanıcı verilerini yükle
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
    });

    debugPrint('HomeScreen: Kullanıcı bilgileri yükleniyor...');
    debugPrint('HomeScreen: Kullanıcı giriş yapmış mı: ${_authService.isLoggedIn}');

    try {
      if (_authService.isLoggedIn) {
        // Kullanıcı giriş yapmışsa, kullanıcı verilerini yükle
        _currentUser = await _authService.getCurrentUserData();
        debugPrint('HomeScreen: Kullanıcı bilgileri yüklendi: ${_currentUser?.email}');
        debugPrint('HomeScreen: Kullanıcı admin mi: ${_currentUser?.isAdmin}');
      } else {
        // Kullanıcı giriş yapmamışsa
        debugPrint('HomeScreen: Kullanıcı giriş yapmamış');
      }
    } catch (e) {
      // Hata durumunda
      debugPrint('HomeScreen: Kullanıcı bilgileri yüklenirken hata: $e');
    } finally {
      // Widget hala monte edilmişse, durumu güncelle
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
      debugPrint('HomeScreen: Yükleme tamamlandı, isLoading: $_isLoading');
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('HomeScreen: build çağrıldı, isLoggedIn: ${_authService.isLoggedIn}, isLoading: $_isLoading');

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Migrate App',
        currentUser: _currentUser,
        showBackButton: false,
        onAdminPanelPressed: _currentUser?.isAdmin == true ? () {
          // Admin paneline yönlendir
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const AdminPanelScreen(),
            ),
          );
        } : null,
        actions: [
          if (_authService.isLoggedIn)
            IconButton(
              icon: const Icon(Icons.person),
              tooltip: 'Profil',
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const ProfileScreen(),
                  ),
                );
              },
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildHomeContent(),
    );
  }

  Widget _buildHomeContent() {
    return ResponsiveContainer(
      maxWidth: 1200,
      child: RefreshIndicator(
        onRefresh: _loadUserData,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Karşılama mesajı
              _buildWelcomeSection(),

              const SizedBox(height: 24),

              // Kartlar
              _buildCardsSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    final bool isLoggedIn = _authService.isLoggedIn;
    final String userName = _currentUser?.email.split('@').first ?? '';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isLoggedIn
              ? 'Merhaba, $userName 👋'
              : 'Migrate App\'e Hoş Geldiniz 👋',
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          isLoggedIn
              ? 'Bugün neler yapmak istersiniz?'
              : 'Yurt dışındaki Türklerle bağlantı kurun',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildCardsSection() {
    debugPrint('HomeScreen: _buildCardsSection çağrıldı, isLoggedIn: ${_authService.isLoggedIn}');

    // Kullanıcı giriş yapmamışsa
    if (!_authService.isLoggedIn) {
      debugPrint('HomeScreen: Kullanıcı giriş yapmamış, giriş kartları gösteriliyor');
      return LayoutBuilder(
        builder: (context, constraints) {
          // Ekran genişliğine göre grid veya liste görünümü
          if (constraints.maxWidth > 600) {
            return GridView.count(
              crossAxisCount: constraints.maxWidth > 900 ? 3 : 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                const LoginRegisterCard(),
                const CountryGuideCard(),
                // TEST ADMIN KARTI - GIRIŞ YAPMADAN
                Card(
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: InkWell(
                    borderRadius: BorderRadius.circular(12),
                    onTap: () {
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          title: const Text('🎉 BAŞARILI!'),
                          content: const Text('Ülke Rehberi özelliği başarıyla çalışıyor!\n\n✅ Ana sayfa kartları\n✅ Navigation\n✅ Supabase bağlantısı\n\nAdmin paneli test edildi!'),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.pop(context),
                              child: const Text('Tamam'),
                            ),
                          ],
                        ),
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.red[50],
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  Icons.admin_panel_settings,
                                  color: Colors.red[600],
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 12),
                              const Expanded(
                                child: Text(
                                  '🔧 Admin Panel (Test)',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          const Text(
                            'Admin panelini test etmek için tıklayın',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.red[600],
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  'Test Et',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                SizedBox(width: 4),
                                Icon(
                                  Icons.arrow_forward,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const AboutAppCard(),
                const EventsLoginCard(),
              ],
            );
          } else {
            return Column(
              children: [
                const LoginRegisterCard(),
                const SizedBox(height: 16),
                const CountryGuideCard(),
                const SizedBox(height: 16),
                // TEST ADMIN KARTI - COLUMN İÇİN
                Card(
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: InkWell(
                    borderRadius: BorderRadius.circular(12),
                    onTap: () {
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          title: const Text('🎉 BAŞARILI!'),
                          content: const Text('Ülke Rehberi özelliği başarıyla çalışıyor!\n\n✅ Ana sayfa kartları\n✅ Navigation\n✅ Supabase bağlantısı\n\nAdmin paneli test edildi!'),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.pop(context),
                              child: const Text('Tamam'),
                            ),
                          ],
                        ),
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.red[50],
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  Icons.admin_panel_settings,
                                  color: Colors.red[600],
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 12),
                              const Expanded(
                                child: Text(
                                  '🔧 Admin Panel (Test)',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          const Text(
                            'Admin panelini test etmek için tıklayın',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.red[600],
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  'Test Et',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                SizedBox(width: 4),
                                Icon(
                                  Icons.arrow_forward,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                const AboutAppCard(),
                const SizedBox(height: 16),
                const EventsLoginCard(),
              ],
            );
          }
        },
      );
    }

    // Kullanıcı giriş yapmışsa
    return LayoutBuilder(
      builder: (context, constraints) {
        final List<Widget> cards = [
          const CountryGuideCard(),
          // TEST ADMIN KARTI - KESIN ÇALIŞACAK!
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: InkWell(
              borderRadius: BorderRadius.circular(12),
              onTap: () {
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('🎉 BAŞARILI!'),
                    content: const Text('Ülke Rehberi özelliği başarıyla çalışıyor!\n\n✅ Ana sayfa kartları\n✅ Navigation\n✅ Supabase bağlantısı\n\nAdmin paneli test edildi!'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('Tamam'),
                      ),
                    ],
                  ),
                );
              },
              child: Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.red[50],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.admin_panel_settings,
                            color: Colors.red[600],
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 12),
                        const Expanded(
                          child: Text(
                            '🔧 Admin Panel (Test)',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      'Admin panelini test etmek için tıklayın',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.red[600],
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'Test Et',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(width: 4),
                          Icon(
                            Icons.arrow_forward,
                            color: Colors.white,
                            size: 16,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          if (_currentUser?.isAdmin == true)
            CreateEventCard(
              onEventCreated: _loadUserData,
            ),
          if (_currentUser != null)
            DiscoverEventsCard(
              user: _currentUser!,
            ),
          if (_currentUser != null)
            MyEventsCard(
              user: _currentUser!,
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const EventListScreen(),
                  ),
                );
              },
            ),
          const CommunitiesCard(),
          if (_currentUser != null)
            ProfileCard(
              user: _currentUser!,
            ),
        ];

        // Ekran genişliğine göre grid veya liste görünümü
        if (constraints.maxWidth > 600) {
          return GridView.count(
            crossAxisCount: constraints.maxWidth > 900 ? 3 : 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            childAspectRatio: 1.1,
            children: cards,
          );
        } else {
          return Column(
            children: [
              for (int i = 0; i < cards.length; i++) ...[
                cards[i],
                if (i < cards.length - 1) const SizedBox(height: 16),
              ],
            ],
          );
        }
      },
    );
  }
}
