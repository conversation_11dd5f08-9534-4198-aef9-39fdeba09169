import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import '../models/event_model.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';
import '../services/event_service.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/responsive_container.dart';

class EventDetailScreen extends StatefulWidget {
  final Event event;

  const EventDetailScreen({
    Key? key,
    required this.event,
  }) : super(key: key);

  @override
  State<EventDetailScreen> createState() => _EventDetailScreenState();
}

class _EventDetailScreenState extends State<EventDetailScreen> {
  final EventService _eventService = EventService();
  final AuthService _authService = AuthService();
  bool _isLoading = false;
  late Future<List<AppUser>> _participantsFuture;
  AppUser? _currentUser;

  @override
  void initState() {
    super.initState();
    _loadCurrentUser();
    _loadParticipants();
  }

  Future<void> _loadCurrentUser() async {
    _currentUser = await _authService.getCurrentUserData();
    setState(() {});
  }

  void _loadParticipants() {
    _participantsFuture = _eventService.getEventParticipants(widget.event.id);
  }

  bool get _isParticipating => 
      widget.event.isParticipating(_authService.currentUser?.id ?? '');

  bool get _isPastEvent => widget.event.date.isBefore(DateTime.now());

  Future<void> _toggleParticipation() async {
    if (_isPastEvent) {
      Fluttertoast.showToast(
        msg: "Geçmiş etkinliklere katılamazsınız",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      if (_isParticipating) {
        await _eventService.leaveEvent(
          widget.event.id, 
          _authService.currentUser?.id ?? '',
        );
        if (mounted) {
          Fluttertoast.showToast(
            msg: "Etkinlikten ayrıldınız",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM,
          );
        }
      } else {
        await _eventService.joinEvent(
          widget.event.id, 
          _authService.currentUser?.id ?? '',
        );
        if (mounted) {
          Fluttertoast.showToast(
            msg: "Etkinliğe katıldınız",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM,
          );
        }
      }

      // Refresh event data
      setState(() {
        _loadParticipants();
      });
    } catch (e) {
      if (mounted) {
        Fluttertoast.showToast(
          msg: "İşlem başarısız: ${e.toString()}",
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.red,
          textColor: Colors.white,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _deleteEvent() async {
    // Show confirmation dialog
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Etkinliği Sil'),
        content: const Text(
          'Bu etkinliği silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('İptal'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text(
              'Sil',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );

    if (confirm != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await _eventService.deleteEvent(widget.event.id);
      
      if (mounted) {
        Fluttertoast.showToast(
          msg: "Etkinlik silindi",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
        );
        Navigator.of(context).pop(true); // Return true to indicate deletion
      }
    } catch (e) {
      if (mounted) {
        Fluttertoast.showToast(
          msg: "Silme işlemi başarısız: ${e.toString()}",
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.red,
          textColor: Colors.white,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Etkinlik Detayı',
        currentUser: _currentUser,
        actions: [
          if (_currentUser?.isAdmin == true)
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: _isLoading ? null : _deleteEvent,
              color: Colors.red,
            ),
        ],
      ),
      body: ResponsiveContainer(
        maxWidth: 800,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _buildEventDetail(),
      ),
    );
  }

  Widget _buildEventDetail() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Event image
          if (widget.event.imageUrl.isNotEmpty)
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.network(
                widget.event.imageUrl,
                height: 200,
                width: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    height: 200,
                    color: Colors.grey[300],
                    child: const Center(
                      child: Icon(
                        Icons.image_not_supported,
                        size: 50,
                        color: Colors.grey,
                      ),
                    ),
                  );
                },
              ),
            ),
          const SizedBox(height: 16),
          
          // Event title
          Text(
            widget.event.title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          
          // Event metadata
          Row(
            children: [
              const Icon(Icons.location_on, size: 16, color: Colors.grey),
              const SizedBox(width: 4),
              Text(
                widget.event.city,
                style: const TextStyle(color: Colors.grey),
              ),
              const SizedBox(width: 16),
              const Icon(Icons.calendar_today, size: 16, color: Colors.grey),
              const SizedBox(width: 4),
              Text(
                widget.event.formattedDate,
                style: TextStyle(
                  color: _isPastEvent ? Colors.red : Colors.grey,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Event description
          const Text(
            'Açıklama',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(widget.event.description),
          const SizedBox(height: 24),
          
          // Participation button
          if (!_isPastEvent)
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _toggleParticipation,
                icon: Icon(_isParticipating ? Icons.person_remove : Icons.person_add),
                label: Text(_isParticipating ? 'Etkinlikten Ayrıl' : 'Etkinliğe Katıl'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isParticipating ? Colors.red : null,
                ),
              ),
            ),
          
          const SizedBox(height: 24),
          const Divider(),
          
          // Participants section
          const Text(
            'Katılımcılar',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          _buildParticipantsList(),
        ],
      ),
    );
  }

  Widget _buildParticipantsList() {
    return FutureBuilder<List<AppUser>>(
      future: _participantsFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text('Hata: ${snapshot.error}'),
          );
        }

        final participants = snapshot.data ?? [];

        if (participants.isEmpty) {
          return const Padding(
            padding: EdgeInsets.symmetric(vertical: 16.0),
            child: Center(
              child: Text('Henüz katılımcı yok'),
            ),
          );
        }

        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: participants.length,
          itemBuilder: (context, index) {
            final participant = participants[index];
            return ListTile(
              leading: CircleAvatar(
                child: Text(participant.email[0].toUpperCase()),
              ),
              title: Text(participant.email),
              subtitle: Text('${participant.city}, ${participant.country}'),
              trailing: participant.isAdmin
                  ? const Chip(
                      label: Text(
                        'Admin',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                      ),
                      backgroundColor: Colors.red,
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                      visualDensity: VisualDensity.compact,
                    )
                  : null,
            );
          },
        );
      },
    );
  }
}
