-- <PERSON><PERSON><PERSON> sayfaları tablosu oluşturma
-- <PERSON><PERSON> dosyayı Supabase SQL Editor'da çalıştırın

-- 1. country_pages tablosunu oluştur
CREATE TABLE IF NOT EXISTS country_pages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    country TEXT NOT NULL UNIQUE,
    json_content JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. <PERSON><PERSON><PERSON><PERSON> oluştur (performans için)
CREATE INDEX IF NOT EXISTS idx_country_pages_country ON country_pages(country);
CREATE INDEX IF NOT EXISTS idx_country_pages_created_at ON country_pages(created_at);
CREATE INDEX IF NOT EXISTS idx_country_pages_updated_at ON country_pages(updated_at);

-- <PERSON><PERSON><PERSON> içeriği için GIN indeksi (arama performansı için)
CREATE INDEX IF NOT EXISTS idx_country_pages_json_content ON country_pages USING GIN(json_content);

-- 3. RLS (Row Level Security) politikaları
ALTER TABLE country_pages ENABLE ROW LEVEL SECURITY;

-- Herkes okuyabilir (public read access)
CREATE POLICY "Anyone can read country pages" ON country_pages
    FOR SELECT USING (true);

-- Sadece adminler yazabilir (admin write access)
CREATE POLICY "Only admins can insert country pages" ON country_pages
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM admins 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Only admins can update country pages" ON country_pages
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM admins 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Only admins can delete country pages" ON country_pages
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM admins 
            WHERE user_id = auth.uid()
        )
    );

-- 4. Trigger fonksiyonu - updated_at otomatik güncelleme
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 5. Trigger oluştur
CREATE TRIGGER update_country_pages_updated_at 
    BEFORE UPDATE ON country_pages 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 6. JSON şema validasyonu fonksiyonu
CREATE OR REPLACE FUNCTION validate_country_page_json(json_data JSONB)
RETURNS BOOLEAN AS $$
BEGIN
    -- JSON'da gerekli alanların varlığını kontrol et
    IF NOT (json_data ? 'country' AND json_data ? 'sections') THEN
        RETURN FALSE;
    END IF;
    
    -- country alanının string olduğunu kontrol et
    IF NOT (jsonb_typeof(json_data->'country') = 'string') THEN
        RETURN FALSE;
    END IF;
    
    -- sections alanının array olduğunu kontrol et
    IF NOT (jsonb_typeof(json_data->'sections') = 'array') THEN
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- 7. JSON validasyon constraint'i
ALTER TABLE country_pages 
ADD CONSTRAINT valid_json_content 
CHECK (validate_country_page_json(json_content));

-- 8. Örnek veri ekleme (test için)
INSERT INTO country_pages (country, json_content) VALUES 
('Hollanda', '{
  "country": "Hollanda",
  "sections": [
    {
      "title": "Hollanda''da Yaşam",
      "category": "Genel Bilgi",
      "icon": "🏡",
      "imageUrl": "https://images.unsplash.com/photo-1534351590666-13e3e96b5017?w=800",
      "content": "Hollanda; yüksek yaşam kalitesi, bisiklet dostu şehirleri ve sosyal hakları ile öne çıkar. Ülke, düzenli şehir planlaması ve çevre dostu yaşam tarzıyla tanınır.",
      "links": [
        {
          "label": "Resmi Göçmenlik Sitesi",
          "url": "https://ind.nl"
        },
        {
          "label": "Yaşam Masrafları Hesaplayıcı",
          "url": "https://numbeo.com/cost-of-living/country_result.jsp?country=Netherlands"
        }
      ],
      "videoUrl": null,
      "tags": ["yaşam", "şehirler", "sosyal hayat", "bisiklet"]
    },
    {
      "title": "Nasıl Gidilir?",
      "category": "Göç & Vize",
      "icon": "✈️",
      "imageUrl": "https://images.unsplash.com/photo-1436491865332-7a61a109cc05?w=800",
      "content": "Startup vizesi, au-pair programı, yüksek lisans ve iş vizesi gibi farklı yollarla Hollanda''ya göç etmek mümkündür. Her yolun kendine özgü şartları vardır.",
      "links": [
        {
          "label": "Hollanda Vize Başvuru",
          "url": "https://www.vfsvisaonline.com/Netherlands-Global-Online-Appointment_Zone2/AppScheduling/AppWelcome.aspx?P=hQem3fbSoVv8NaZKJBBKew%3d%3d"
        },
        {
          "label": "IND Resmi Sitesi",
          "url": "https://ind.nl/en"
        }
      ],
      "videoUrl": null,
      "tags": ["vize", "başvuru", "taşınma", "göç"]
    },
    {
      "title": "İş İmkanları",
      "category": "Kariyer",
      "icon": "💼",
      "imageUrl": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800",
      "content": "IT, sağlık, lojistik ve mühendislik sektörlerinde Türkler için ciddi fırsatlar mevcut. Hollanda''da iş bulma süreci ve networking önemlidir.",
      "links": [
        {
          "label": "LinkedIn Hollanda İş İlanları",
          "url": "https://www.linkedin.com/jobs/search/?location=Netherlands"
        },
        {
          "label": "Indeed Hollanda",
          "url": "https://nl.indeed.com/"
        }
      ],
      "videoUrl": null,
      "tags": ["iş", "kariyer", "network", "teknoloji"]
    },
    {
      "title": "Türk Topluluğu",
      "category": "Topluluk",
      "icon": "🤝",
      "imageUrl": "https://images.unsplash.com/photo-1529156069898-49953e39b3ac?w=800",
      "content": "Hollanda''daki Türk topluluğu genç, eğitimli ve çoğunlukla şehir merkezlerinde yaşıyor. Amsterdam, Rotterdam ve Den Haag''da aktif Türk toplulukları bulunur.",
      "links": [
        {
          "label": "Hollanda Türk Dernekleri",
          "url": "https://www.turkishdutch.nl/"
        }
      ],
      "videoUrl": null,
      "tags": ["topluluk", "sosyalleşme", "türkler", "etkinlik"]
    }
  ]
}')
ON CONFLICT (country) DO NOTHING;

-- 9. Almanya örnek verisi
INSERT INTO country_pages (country, json_content) VALUES 
('Almanya', '{
  "country": "Almanya",
  "sections": [
    {
      "title": "Almanya''da Yaşam",
      "category": "Genel Bilgi", 
      "icon": "🏰",
      "imageUrl": "https://images.unsplash.com/photo-1467269204594-9661b134dd2b?w=800",
      "content": "Almanya güçlü ekonomisi, kaliteli eğitim sistemi ve sosyal güvenlik ağıyla öne çıkar. Ülke, çok kültürlü yapısı ve iş imkanlarıyla göçmenler için cazip bir destinasyondur.",
      "links": [
        {
          "label": "Make-it-in-germany.com",
          "url": "https://www.make-it-in-germany.com/en/"
        },
        {
          "label": "Almanya Yaşam Rehberi",
          "url": "https://www.germany.travel/en/home.html"
        }
      ],
      "videoUrl": null,
      "tags": ["yaşam", "ekonomi", "sosyal güvenlik"]
    },
    {
      "title": "Vize ve Göç",
      "category": "Göç & Vize",
      "icon": "📋",
      "imageUrl": "https://images.unsplash.com/photo-1554224155-8d04cb21cd6c?w=800",
      "content": "Almanya''ya çeşitli vize türleriyle giriş yapılabilir: iş vizesi, öğrenci vizesi, aile birleşimi ve mavi kart gibi seçenekler mevcuttur.",
      "links": [
        {
          "label": "Almanya Konsolosluğu",
          "url": "https://tuerkei.diplo.de/tr-tr"
        }
      ],
      "videoUrl": null,
      "tags": ["vize", "mavi kart", "göç"]
    }
  ]
}')
ON CONFLICT (country) DO NOTHING;

-- 10. Veritabanı fonksiyonları ve görünümler

-- Ülke sayısını döndüren fonksiyon
CREATE OR REPLACE FUNCTION get_country_count()
RETURNS INTEGER AS $$
BEGIN
    RETURN (SELECT COUNT(*) FROM country_pages);
END;
$$ LANGUAGE plpgsql;

-- En son güncellenen ülkeleri döndüren görünüm
CREATE OR REPLACE VIEW recent_country_updates AS
SELECT 
    country,
    updated_at,
    json_content->>'country' as country_name,
    jsonb_array_length(json_content->'sections') as section_count
FROM country_pages 
ORDER BY updated_at DESC 
LIMIT 10;

-- Kategori istatistikleri görünümü
CREATE OR REPLACE VIEW category_stats AS
WITH section_categories AS (
    SELECT 
        country,
        jsonb_array_elements(json_content->'sections')->>'category' as category
    FROM country_pages
)
SELECT 
    category,
    COUNT(*) as usage_count,
    array_agg(DISTINCT country) as countries
FROM section_categories 
GROUP BY category
ORDER BY usage_count DESC;

-- Başarılı tablo oluşturma mesajı
SELECT 'country_pages tablosu başarıyla oluşturuldu!' as message;
