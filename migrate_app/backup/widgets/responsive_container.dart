import 'package:flutter/material.dart';

/// Responsive container that limits the width of the content
/// and centers it on larger screens
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final double maxWidth;
  final double horizontalPadding;
  final Color backgroundColor;

  const ResponsiveContainer({
    Key? key,
    required this.child,
    this.maxWidth = 600.0, // Default max width for mobile-like experience
    this.horizontalPadding = 16.0,
    this.backgroundColor = Colors.white,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: backgroundColor,
      child: Center(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: maxWidth,
          ),
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
          child: child,
        ),
      ),
    );
  }
}
