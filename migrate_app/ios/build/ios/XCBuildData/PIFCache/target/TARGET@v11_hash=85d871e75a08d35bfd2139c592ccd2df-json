{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981a35bccc0f7fc452441efea99bdb9914", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/quill_native_bridge_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "quill_native_bridge_ios", "INFOPLIST_FILE": "Target Support Files/quill_native_bridge_ios/ResourceBundle-quill_native_bridge_ios_privacy-quill_native_bridge_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "quill_native_bridge_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e985f2929a7461bc18ec1a33197e13cec90", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ee9797f74da0811b315c84083c114c8f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/quill_native_bridge_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "quill_native_bridge_ios", "INFOPLIST_FILE": "Target Support Files/quill_native_bridge_ios/ResourceBundle-quill_native_bridge_ios_privacy-quill_native_bridge_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "quill_native_bridge_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98af0416798063f1ea5ea2d07e7b0c9b03", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ee9797f74da0811b315c84083c114c8f", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/quill_native_bridge_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "quill_native_bridge_ios", "INFOPLIST_FILE": "Target Support Files/quill_native_bridge_ios/ResourceBundle-quill_native_bridge_ios_privacy-quill_native_bridge_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "quill_native_bridge_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9822004631c441342f422b8a053f60a171", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fb78735f1d499b6483da86bd8e527587", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fd5ae23c3afbaf836da98df7f62b27e3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fcb9519aca4a1a0b62ac3a233fb1a000", "guid": "bfdfe7dc352907fc980b868725387e9823520a3d8da09a0a4332e87a53197a56"}], "guid": "bfdfe7dc352907fc980b868725387e985526e36bb131e1ba8ab6f69a39a1c10c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9815c568b692670a257cffee120a27e3f5", "name": "quill_native_bridge_ios-quill_native_bridge_ios_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986b20fa06afd49bd8eb14532479b5f0ed", "name": "quill_native_bridge_ios_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}