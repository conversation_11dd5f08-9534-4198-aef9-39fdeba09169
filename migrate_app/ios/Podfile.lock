PODS:
  - app_links (0.0.2):
    - Flutter
  - Flutter (1.0.0)
  - flutter_keyboard_visibility_temp_fork (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.7.4)
  - image_picker_ios (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - quill_native_bridge_ios (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - TOCropViewController (2.7.4)
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - Flutter (from `Flutter`)
  - flutter_keyboard_visibility_temp_fork (from `.symlinks/plugins/flutter_keyboard_visibility_temp_fork/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - quill_native_bridge_ios (from `.symlinks/plugins/quill_native_bridge_ios/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - TOCropViewController

EXTERNAL SOURCES:
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  Flutter:
    :path: Flutter
  flutter_keyboard_visibility_temp_fork:
    :path: ".symlinks/plugins/flutter_keyboard_visibility_temp_fork/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  quill_native_bridge_ios:
    :path: ".symlinks/plugins/quill_native_bridge_ios/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  app_links: 76b66b60cc809390ca1ad69bfd66b998d2387ac7
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_keyboard_visibility_temp_fork: 95b2d534bacf6ac62e7fcbe5c2a9e2c2a17ce06f
  fluttertoast: 2c67e14dce98bbdb200df9e1acf610d7a6264ea1
  image_cropper: 5f162dcf988100dc1513f9c6b7eb42cd6fbf9156
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  quill_native_bridge_ios: f47af4b14e7757968486641656c5d23250cee521
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d

PODFILE CHECKSUM: 20e260c9bb3f61194c661a4ba028da86e4f3ed96

COCOAPODS: 1.16.2
