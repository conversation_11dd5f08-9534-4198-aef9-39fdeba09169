-- Migrate App Database Setup
-- Bu dosyayı Supabase SQL Editor'da çalıştırın

-- 1. Users tablosu (eğer yoksa olu<PERSON>)
CREATE TABLE IF NOT EXISTS users (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email TEXT NOT NULL,
    city TEXT,
    country TEXT DEFAULT 'Türkiye',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Ad<PERSON> tablosu (eğer yoksa oluştur)
CREATE TABLE IF NOT EXISTS admins (
    user_id UUID REFERENCES auth.users(id) PRIMARY KEY
);

-- 3. Events tablosu (eğer yoksa oluş<PERSON>)
CREATE TABLE IF NOT EXISTS events (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    city TEXT,
    date TIMESTAMP WITH TIME ZONE,
    image_url TEXT,
    participants UUID[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Country Pages tablosu (eğer yoksa oluştur)
CREATE TABLE IF NOT EXISTS country_pages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    country TEXT NOT NULL UNIQUE,
    json_content JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. İndeksler oluştur
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_events_date ON events(date);
CREATE INDEX IF NOT EXISTS idx_events_city ON events(city);
CREATE INDEX IF NOT EXISTS idx_country_pages_country ON country_pages(country);
CREATE INDEX IF NOT EXISTS idx_country_pages_json_content ON country_pages USING GIN(json_content);

-- 6. RLS (Row Level Security) politikaları

-- Users tablosu RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read their own data" ON users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own data" ON users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Anyone can insert user data" ON users
    FOR INSERT WITH CHECK (true);

-- Events tablosu RLS
ALTER TABLE events ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read events" ON events
    FOR SELECT USING (true);

CREATE POLICY "Authenticated users can create events" ON events
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Event creators and admins can update events" ON events
    FOR UPDATE USING (
        auth.uid() IN (
            SELECT user_id FROM admins
        )
    );

-- Country Pages tablosu RLS
ALTER TABLE country_pages ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read country pages" ON country_pages
    FOR SELECT USING (true);

CREATE POLICY "Only admins can insert country pages" ON country_pages
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM admins 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Only admins can update country pages" ON country_pages
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM admins 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Only admins can delete country pages" ON country_pages
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM admins 
            WHERE user_id = auth.uid()
        )
    );

-- 7. Trigger fonksiyonu - updated_at otomatik güncelleme
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 8. Trigger oluştur
CREATE TRIGGER update_country_pages_updated_at 
    BEFORE UPDATE ON country_pages 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 9. Test admin kullanıcısı oluşturmak için fonksiyon
-- NOT: Bu fonksiyonu çalıştırdıktan sonra, gerçek bir kullanıcı kaydı yapın
-- ve sonra o kullanıcının ID'sini admins tablosuna ekleyin

-- Örnek admin ekleme (kullanıcı kaydından sonra çalıştırın):
-- INSERT INTO admins (user_id) VALUES ('USER_ID_BURAYA_GELECEK');
