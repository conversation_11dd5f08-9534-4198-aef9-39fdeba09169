-- User profiles tablosu olu<PERSON>tur
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    email TEXT NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    avatar TEXT,
    bio TEXT,
    
    -- Göç Bilgileri
    from_country TEXT DEFAULT '',
    from_city TEXT DEFAULT '',
    to_country TEXT DEFAULT '',
    to_city TEXT DEFAULT '',
    migration_date TIMESTAMPTZ,
    migration_status TEXT DEFAULT 'planning' CHECK (migration_status IN ('planning', 'in-progress', 'completed')),
    
    -- Profesyonel Bilgiler
    profession TEXT DEFAULT '',
    industry TEXT DEFAULT '',
    experience_level TEXT DEFAULT 'mid' CHECK (experience_level IN ('junior', 'mid', 'senior', 'expert')),
    skills TEXT[] DEFAULT '{}',
    current_company TEXT,
    current_position TEXT,
    
    -- Networking Tercihleri
    help_topics TEXT[] DEFAULT '{}', -- ['vize', 'konut', 'iş', 'eğitim', 'sağlık']
    user_categories TEXT[] DEFAULT '{}', -- ['yeni-göçmen', 'deneyimli', 'profesyonel', 'sosyal']
    networking_goals TEXT[] DEFAULT '{}', -- ['iş-bulma', 'arkadaş-edinme', 'yardım-etme', 'öğrenme']
    
    -- İletişim Tercihleri
    is_open_to_mentoring BOOLEAN DEFAULT false,
    is_seeking_mentor BOOLEAN DEFAULT false,
    is_open_to_networking BOOLEAN DEFAULT true,
    preferred_languages TEXT[] DEFAULT '{"tr"}', -- ['tr', 'en', 'nl', 'de']
    
    -- Sosyal Medya
    linkedin_url TEXT,
    twitter_url TEXT,
    instagram_url TEXT,
    website_url TEXT,
    
    -- Sistem Bilgileri
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    is_verified BOOLEAN DEFAULT false,
    is_public BOOLEAN DEFAULT true
);

-- Connections tablosu (networking bağlantıları)
CREATE TABLE IF NOT EXISTS user_connections (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    requester_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    addressee_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'blocked')),
    connection_type TEXT DEFAULT 'professional' CHECK (connection_type IN ('professional', 'casual', 'mentor', 'mentee')),
    message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(requester_id, addressee_id)
);

-- User skills tablosu (detaylı skill yönetimi)
CREATE TABLE IF NOT EXISTS user_skills (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    skill_name TEXT NOT NULL,
    skill_level TEXT DEFAULT 'intermediate' CHECK (skill_level IN ('beginner', 'intermediate', 'advanced', 'expert')),
    years_of_experience INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User experiences tablosu (iş deneyimleri)
CREATE TABLE IF NOT EXISTS user_experiences (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    company_name TEXT NOT NULL,
    position TEXT NOT NULL,
    location TEXT,
    start_date DATE,
    end_date DATE,
    is_current BOOLEAN DEFAULT false,
    description TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User educations tablosu (eğitim bilgileri)
CREATE TABLE IF NOT EXISTS user_educations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    institution_name TEXT NOT NULL,
    degree TEXT NOT NULL,
    field_of_study TEXT,
    location TEXT,
    start_date DATE,
    end_date DATE,
    is_current BOOLEAN DEFAULT false,
    description TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- İndeksler oluştur
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_from_country ON user_profiles(from_country);
CREATE INDEX IF NOT EXISTS idx_user_profiles_to_country ON user_profiles(to_country);
CREATE INDEX IF NOT EXISTS idx_user_profiles_profession ON user_profiles(profession);
CREATE INDEX IF NOT EXISTS idx_user_profiles_experience_level ON user_profiles(experience_level);
CREATE INDEX IF NOT EXISTS idx_user_profiles_help_topics ON user_profiles USING GIN(help_topics);
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_categories ON user_profiles USING GIN(user_categories);
CREATE INDEX IF NOT EXISTS idx_user_profiles_skills ON user_profiles USING GIN(skills);
CREATE INDEX IF NOT EXISTS idx_user_profiles_is_public ON user_profiles(is_public);
CREATE INDEX IF NOT EXISTS idx_user_profiles_is_open_to_networking ON user_profiles(is_open_to_networking);
CREATE INDEX IF NOT EXISTS idx_user_profiles_is_open_to_mentoring ON user_profiles(is_open_to_mentoring);
CREATE INDEX IF NOT EXISTS idx_user_profiles_is_seeking_mentor ON user_profiles(is_seeking_mentor);

CREATE INDEX IF NOT EXISTS idx_user_connections_requester_id ON user_connections(requester_id);
CREATE INDEX IF NOT EXISTS idx_user_connections_addressee_id ON user_connections(addressee_id);
CREATE INDEX IF NOT EXISTS idx_user_connections_status ON user_connections(status);

CREATE INDEX IF NOT EXISTS idx_user_skills_user_id ON user_skills(user_id);
CREATE INDEX IF NOT EXISTS idx_user_experiences_user_id ON user_experiences(user_id);
CREATE INDEX IF NOT EXISTS idx_user_educations_user_id ON user_educations(user_id);

-- RLS (Row Level Security) politikaları
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_connections ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_skills ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_experiences ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_educations ENABLE ROW LEVEL SECURITY;

-- User profiles için politikalar
CREATE POLICY "Public profiles are viewable by everyone" ON user_profiles
    FOR SELECT USING (is_public = true);

CREATE POLICY "Users can view their own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own profile" ON user_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own profile" ON user_profiles
    FOR DELETE USING (auth.uid() = user_id);

-- User connections için politikalar
CREATE POLICY "Users can view their own connections" ON user_connections
    FOR SELECT USING (auth.uid() = requester_id OR auth.uid() = addressee_id);

CREATE POLICY "Users can create connection requests" ON user_connections
    FOR INSERT WITH CHECK (auth.uid() = requester_id);

CREATE POLICY "Users can update their own connections" ON user_connections
    FOR UPDATE USING (auth.uid() = requester_id OR auth.uid() = addressee_id);

CREATE POLICY "Users can delete their own connections" ON user_connections
    FOR DELETE USING (auth.uid() = requester_id OR auth.uid() = addressee_id);

-- User skills için politikalar
CREATE POLICY "Skills are viewable by everyone" ON user_skills
    FOR SELECT USING (true);

CREATE POLICY "Users can insert their own skills" ON user_skills
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own skills" ON user_skills
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own skills" ON user_skills
    FOR DELETE USING (auth.uid() = user_id);

-- User experiences için politikalar
CREATE POLICY "Experiences are viewable by everyone" ON user_experiences
    FOR SELECT USING (true);

CREATE POLICY "Users can insert their own experiences" ON user_experiences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own experiences" ON user_experiences
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own experiences" ON user_experiences
    FOR DELETE USING (auth.uid() = user_id);

-- User educations için politikalar
CREATE POLICY "Educations are viewable by everyone" ON user_educations
    FOR SELECT USING (true);

CREATE POLICY "Users can insert their own educations" ON user_educations
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own educations" ON user_educations
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own educations" ON user_educations
    FOR DELETE USING (auth.uid() = user_id);

-- Trigger fonksiyonları
CREATE OR REPLACE FUNCTION update_user_profiles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_user_connections_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger oluştur
CREATE TRIGGER update_user_profiles_updated_at_trigger
    BEFORE UPDATE ON user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_user_profiles_updated_at();

CREATE TRIGGER update_user_connections_updated_at_trigger
    BEFORE UPDATE ON user_connections
    FOR EACH ROW
    EXECUTE FUNCTION update_user_connections_updated_at();

-- Yardımcı fonksiyonlar
CREATE OR REPLACE FUNCTION get_mutual_connections(user1_id UUID, user2_id UUID)
RETURNS INTEGER AS $$
DECLARE
    mutual_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO mutual_count
    FROM user_connections uc1
    JOIN user_connections uc2 ON (
        (uc1.addressee_id = uc2.addressee_id AND uc1.requester_id != uc2.requester_id) OR
        (uc1.addressee_id = uc2.requester_id AND uc1.requester_id != uc2.addressee_id) OR
        (uc1.requester_id = uc2.addressee_id AND uc1.addressee_id != uc2.requester_id) OR
        (uc1.requester_id = uc2.requester_id AND uc1.addressee_id != uc2.addressee_id)
    )
    WHERE uc1.status = 'accepted' 
    AND uc2.status = 'accepted'
    AND ((uc1.requester_id = user1_id OR uc1.addressee_id = user1_id))
    AND ((uc2.requester_id = user2_id OR uc2.addressee_id = user2_id));
    
    RETURN mutual_count;
END;
$$ LANGUAGE plpgsql;

-- Profil tamamlanma yüzdesi hesaplama fonksiyonu
CREATE OR REPLACE FUNCTION calculate_profile_completion(profile_user_id UUID)
RETURNS INTEGER AS $$
DECLARE
    completion_score INTEGER := 0;
    profile_record RECORD;
BEGIN
    SELECT * INTO profile_record FROM user_profiles WHERE user_id = profile_user_id;
    
    IF profile_record IS NULL THEN
        RETURN 0;
    END IF;
    
    -- Her dolu alan için 10 puan
    IF profile_record.first_name IS NOT NULL AND profile_record.first_name != '' THEN
        completion_score := completion_score + 10;
    END IF;
    
    IF profile_record.last_name IS NOT NULL AND profile_record.last_name != '' THEN
        completion_score := completion_score + 10;
    END IF;
    
    IF profile_record.bio IS NOT NULL AND profile_record.bio != '' THEN
        completion_score := completion_score + 10;
    END IF;
    
    IF profile_record.from_country IS NOT NULL AND profile_record.from_country != '' THEN
        completion_score := completion_score + 10;
    END IF;
    
    IF profile_record.to_country IS NOT NULL AND profile_record.to_country != '' THEN
        completion_score := completion_score + 10;
    END IF;
    
    IF profile_record.profession IS NOT NULL AND profile_record.profession != '' THEN
        completion_score := completion_score + 10;
    END IF;
    
    IF array_length(profile_record.skills, 1) > 0 THEN
        completion_score := completion_score + 10;
    END IF;
    
    IF array_length(profile_record.help_topics, 1) > 0 THEN
        completion_score := completion_score + 10;
    END IF;
    
    IF array_length(profile_record.user_categories, 1) > 0 THEN
        completion_score := completion_score + 10;
    END IF;
    
    IF array_length(profile_record.networking_goals, 1) > 0 THEN
        completion_score := completion_score + 10;
    END IF;
    
    RETURN completion_score;
END;
$$ LANGUAGE plpgsql;
