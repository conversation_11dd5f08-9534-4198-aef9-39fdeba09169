import 'package:flutter/material.dart';
import '../models/user_model.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final AppUser? currentUser;
  final List<Widget>? actions;
  final VoidCallback? onAdminPanelPressed;
  final bool showBackButton;

  const CustomAppBar({
    Key? key,
    required this.title,
    this.currentUser,
    this.actions,
    this.onAdminPanelPressed,
    this.showBackButton = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isAdmin = currentUser?.isAdmin ?? false;
    
    return AppBar(
      title: Text(title),
      centerTitle: true,
      elevation: 2,
      automaticallyImplyLeading: showBackButton,
      actions: [
        // Admin badge if user is admin
        if (isAdmin)
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: Chip(
              label: const Text(
                'Admin',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
              backgroundColor: Colors.red,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              visualDensity: VisualDensity.compact,
            ),
          ),
        
        // Admin panel button if user is admin
        if (isAdmin && onAdminPanelPressed != null)
          IconButton(
            icon: const Icon(Icons.admin_panel_settings),
            tooltip: 'Admin Paneli',
            onPressed: onAdminPanelPressed,
          ),
          
        // Other actions
        if (actions != null) ...actions!,
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
