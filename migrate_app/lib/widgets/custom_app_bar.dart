import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../screens/home_screen.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final AppUser? currentUser;
  final List<Widget>? actions;
  final VoidCallback? onAdminPanelPressed;
  final bool showBackButton;
  final bool showHomeButton;

  const CustomAppBar({
    Key? key,
    required this.title,
    this.currentUser,
    this.actions,
    this.onAdminPanelPressed,
    this.showBackButton = true,
    this.showHomeButton = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isAdmin = currentUser?.isAdmin ?? false;

    return AppBar(
      title: GestureDetector(
        onTap: showHomeButton ? () {
          // Ana sayfaya git
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const HomeScreen()),
            (route) => false,
          );
        } : null,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Logo
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(6),
              ),
              child: const Text(
                'MC',
                style: TextStyle(
                  color: Colors.blue,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
            const SizedBox(width: 8),
            // Title
            Flexible(
              child: Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
      centerTitle: true,
      elevation: 2,
      automaticallyImplyLeading: showBackButton,
      actions: [
        // Admin badge if user is admin
        if (isAdmin)
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: Chip(
              label: const Text(
                'Admin',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
              backgroundColor: Colors.red,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              visualDensity: VisualDensity.compact,
            ),
          ),
        
        // Admin panel button if user is admin
        if (isAdmin && onAdminPanelPressed != null)
          IconButton(
            icon: const Icon(Icons.admin_panel_settings),
            tooltip: 'Admin Paneli',
            onPressed: onAdminPanelPressed,
          ),
          
        // Other actions
        if (actions != null) ...actions!,
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
