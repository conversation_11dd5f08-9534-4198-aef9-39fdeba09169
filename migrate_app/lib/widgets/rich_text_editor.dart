import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class RichTextEditor extends StatefulWidget {
  final String initialText;
  final Function(String) onTextChanged;
  final String? label;
  final int minLines;
  final int maxLines;

  const RichTextEditor({
    Key? key,
    required this.initialText,
    required this.onTextChanged,
    this.label,
    this.minLines = 5,
    this.maxLines = 15,
  }) : super(key: key);

  @override
  State<RichTextEditor> createState() => _RichTextEditorState();
}

class _RichTextEditorState extends State<RichTextEditor> {
  late TextEditingController _controller;
  final FocusNode _focusNode = FocusNode();

  // Formatting states
  bool _isBold = false;
  bool _isItalic = false;
  bool _isUnderline = false;
  String _selectedFont = 'Roboto';
  double _selectedSize = 16.0;

  final List<String> _fontFamilies = [
    'Roboto',
    'Arial',
    'Times New Roman',
    'Helvetica',
    'Georgia',
    'Verdana',
    'Courier New',
  ];

  final List<double> _fontSizes = [10, 12, 14, 16, 18, 20, 24, 28, 32];

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialText);
    _controller.addListener(_onTextChanged);
  }

  void _onTextChanged() {
    widget.onTextChanged(_controller.text);
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _insertText(String prefix, String suffix) {
    final text = _controller.text;
    final selection = _controller.selection;

    if (selection.isValid) {
      final selectedText = text.substring(selection.start, selection.end);
      final newText = text.replaceRange(
        selection.start,
        selection.end,
        '$prefix$selectedText$suffix',
      );

      _controller.value = TextEditingValue(
        text: newText,
        selection: TextSelection.collapsed(
          offset: selection.start + prefix.length + selectedText.length + suffix.length,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
        ],

        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade400),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              // Toolbar
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  children: [
                    // Font Family Dropdown
                    DropdownButton<String>(
                      value: _selectedFont,
                      items: _fontFamilies.map((font) => DropdownMenuItem(
                        value: font,
                        child: Text(font, style: TextStyle(fontFamily: font)),
                      )).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedFont = value;
                          });
                        }
                      },
                    ),

                    const SizedBox(width: 8),

                    // Font Size Dropdown
                    DropdownButton<double>(
                      value: _selectedSize,
                      items: _fontSizes.map((size) => DropdownMenuItem(
                        value: size,
                        child: Text('${size.toInt()}'),
                      )).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedSize = value;
                          });
                        }
                      },
                    ),

                    const SizedBox(width: 8),

                    // Bold Button
                    IconButton(
                      icon: Icon(Icons.format_bold),
                      color: _isBold ? Colors.blue : Colors.grey,
                      onPressed: () {
                        setState(() {
                          _isBold = !_isBold;
                        });
                        _insertText('**', '**');
                      },
                    ),

                    // Italic Button
                    IconButton(
                      icon: Icon(Icons.format_italic),
                      color: _isItalic ? Colors.blue : Colors.grey,
                      onPressed: () {
                        setState(() {
                          _isItalic = !_isItalic;
                        });
                        _insertText('*', '*');
                      },
                    ),

                    // Underline Button
                    IconButton(
                      icon: Icon(Icons.format_underlined),
                      color: _isUnderline ? Colors.blue : Colors.grey,
                      onPressed: () {
                        setState(() {
                          _isUnderline = !_isUnderline;
                        });
                        _insertText('<u>', '</u>');
                      },
                    ),

                    const SizedBox(width: 8),

                    // List Button
                    IconButton(
                      icon: Icon(Icons.format_list_bulleted),
                      onPressed: () {
                        _insertText('• ', '');
                      },
                    ),

                    // Numbered List Button
                    IconButton(
                      icon: Icon(Icons.format_list_numbered),
                      onPressed: () {
                        _insertText('1. ', '');
                      },
                    ),
                  ],
                ),
              ),

              // Editor
              Container(
                constraints: BoxConstraints(
                  minHeight: widget.minLines * 24.0,
                  maxHeight: widget.maxLines * 24.0,
                ),
                child: TextFormField(
                  controller: _controller,
                  focusNode: _focusNode,
                  maxLines: null,
                  expands: true,
                  textAlignVertical: TextAlignVertical.top,
                  style: TextStyle(
                    fontFamily: _selectedFont,
                    fontSize: _selectedSize,
                    fontWeight: _isBold ? FontWeight.bold : FontWeight.normal,
                    fontStyle: _isItalic ? FontStyle.italic : FontStyle.normal,
                    decoration: _isUnderline ? TextDecoration.underline : TextDecoration.none,
                  ),
                  decoration: const InputDecoration(
                    hintText: 'İçeriğinizi buraya yazın...',
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.all(16),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Yardım metni
        Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            'Toolbar\'daki araçları kullanarak metninizi biçimlendirebilirsiniz',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
        ),
      ],
    );
  }
}
