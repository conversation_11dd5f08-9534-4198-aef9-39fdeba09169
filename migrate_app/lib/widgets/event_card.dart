import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../models/event_model.dart';
import '../services/event_service.dart';
import '../screens/event_detail_screen.dart';

class EventCard extends StatefulWidget {
  final Event event;
  final String currentUserId;
  final Function onParticipationToggled;
  final bool isAdmin;

  const EventCard({
    Key? key,
    required this.event,
    required this.currentUserId,
    required this.onParticipationToggled,
    required this.isAdmin,
  }) : super(key: key);

  @override
  State<EventCard> createState() => _EventCardState();
}

class _EventCardState extends State<EventCard> {
  final EventService _eventService = EventService();
  bool _isLoading = false;

  bool get _isParticipating => widget.event.isParticipating(widget.currentUserId);

  Future<void> _toggleParticipation() async {
    if (widget.currentUserId.isEmpty) {
      Fluttertoast.showToast(
        msg: "<PERSON>ütfen önce giriş yapın",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      if (_isParticipating) {
        await _eventService.leaveEvent(widget.event.id, widget.currentUserId);
        if (mounted) {
          Fluttertoast.showToast(
            msg: "Etkinlikten ayrıldınız",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM,
          );
        }
      } else {
        await _eventService.joinEvent(widget.event.id, widget.currentUserId);
        if (mounted) {
          Fluttertoast.showToast(
            msg: "Etkinliğe katıldınız",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM,
          );
        }
      }

      widget.onParticipationToggled();
    } catch (e) {
      if (mounted) {
        Fluttertoast.showToast(
          msg: "İşlem başarısız: ${e.toString()}",
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.red,
          textColor: Colors.white,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _deleteEvent() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Etkinliği Sil'),
        content: const Text('Bu etkinliği silmek istediğinizden emin misiniz?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('İptal'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Sil'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        await _eventService.deleteEvent(widget.event.id);
        if (mounted) {
          Fluttertoast.showToast(
            msg: "Etkinlik silindi",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM,
          );
          widget.onParticipationToggled();
        }
      } catch (e) {
        if (mounted) {
          Fluttertoast.showToast(
            msg: "Etkinlik silinemedi: ${e.toString()}",
            toastLength: Toast.LENGTH_LONG,
            gravity: ToastGravity.BOTTOM,
            backgroundColor: Colors.red,
            textColor: Colors.white,
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool isPastEvent = widget.event.date.isBefore(DateTime.now());

    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => EventDetailScreen(event: widget.event),
          ),
        ).then((result) {
          // Refresh the list if the event was updated or deleted
          if (result == true) {
            widget.onParticipationToggled();
          }
        });
      },
      child: Card(
        margin: const EdgeInsets.only(bottom: 16),
        clipBehavior: Clip.antiAlias,
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
          if (widget.event.imageUrl.isNotEmpty)
            Image.network(
              widget.event.imageUrl,
              height: 150,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  height: 150,
                  color: Colors.grey[300],
                  child: const Center(
                    child: Icon(
                      Icons.image_not_supported,
                      size: 50,
                      color: Colors.grey,
                    ),
                  ),
                );
              },
            ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        widget.event.title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    if (widget.isAdmin)
                      IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: _isLoading ? null : _deleteEvent,
                        color: Colors.red,
                      ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.location_on, size: 16, color: Colors.grey),
                    const SizedBox(width: 4),
                    Text(
                      widget.event.city,
                      style: const TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Icon(Icons.calendar_today, size: 16, color: Colors.grey),
                    const SizedBox(width: 4),
                    Text(
                      widget.event.formattedDate,
                      style: TextStyle(
                        color: isPastEvent ? Colors.red : Colors.grey,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(widget.event.description),
                const SizedBox(height: 16),
                Row(
                  children: [
                    const Icon(Icons.people, size: 16),
                    const SizedBox(width: 4),
                    Text('${widget.event.participantCount} katılımcı'),
                  ],
                ),
                const SizedBox(height: 16),
                if (!isPastEvent)
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _toggleParticipation,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _isParticipating ? Colors.red : Colors.blue,
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                          : Text(_isParticipating ? 'Ayrıl' : 'Katıl'),
                    ),
                  )
                else
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Text(
                      'Bu etkinlik sona erdi',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    ),
    );
  }
}
