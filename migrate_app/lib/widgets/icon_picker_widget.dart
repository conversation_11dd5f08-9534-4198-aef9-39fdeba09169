import 'package:flutter/material.dart';

class IconPickerWidget extends StatefulWidget {
  final String currentIcon;
  final Function(String) onIconSelected;

  const IconPickerWidget({
    Key? key,
    required this.currentIcon,
    required this.onIconSelected,
  }) : super(key: key);

  @override
  State<IconPickerWidget> createState() => _IconPickerWidgetState();
}

class _IconPickerWidgetState extends State<IconPickerWidget> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  // Popüler ikonlar kategorilere göre
  final Map<String, List<String>> _iconCategories = {
    'Yaşam': [
      '🏠', '🏡', '🏢', '🏣', '🏤', '🏥', '🏦', '🏧', '🏨', '🏩',
      '🏪', '🏫', '🏬', '🏭', '🏮', '🏯', '🏰', '🗼', '🗽', '⛪',
      '🕌', '🛕', '⛩️', '🕍', '🚗', '🚕', '🚙', '🚌', '🚎', '🏎️',
      '🚓', '🚑', '🚒', '🚐', '🛻', '🚚', '🚛', '🚜', '🏍️', '🛵',
      '🚲', '🛴', '🛹', '🛼', '🚁', '✈️', '🛫', '🛬', '🪂', '💺',
      '🚀', '🛸', '🚉', '🚊', '🚝', '🚞', '🚋', '🚃', '🚂', '🚄',
      '🚅', '🚈', '🚇', '🚆', '🚘', '🚖'
    ],
    'Göç': [
      '🛂', '🛃', '🛄', '🛅', '✈️', '🌍', '🌎', '🌏', '🗺️', '🧳',
      '👨‍💼', '👩‍💼', '📋', '📄', '📃', '📑', '📊', '📈', '📉', '📇',
      '🗂️', '📁', '📂', '🗃️', '🗄️', '📅', '📆', '🗓️', '📝', '📖',
      '📚', '📓', '📔', '📒', '📕', '📗', '📘', '📙', '📰', '🗞️',
      '📜', '📃', '📄', '📑', '🔖', '🏷️', '💼', '👔', '🎓', '🏆'
    ],
    'İş': [
      '💼', '👔', '👨‍💼', '👩‍💼', '👨‍💻', '👩‍💻', '💻', '🖥️', '⌨️', '🖱️',
      '🖨️', '📱', '☎️', '📞', '📟', '📠', '📧', '📨', '📩', '📤',
      '📥', '📦', '📫', '📪', '📬', '📭', '📮', '🗳️', '✏️', '✒️',
      '🖋️', '🖊️', '🖌️', '🖍️', '📝', '💼', '📊', '📈', '📉', '📋',
      '📌', '📍', '📎', '🖇️', '📏', '📐', '✂️', '🗃️', '🗄️', '🗂️'
    ],
    'Topluluk': [
      '👥', '👫', '👬', '👭', '👪', '👨‍👩‍👧‍👦', '👨‍👩‍👧', '👨‍👩‍👦‍👦', '👨‍👩‍👧‍👧', '👨‍👨‍👧',
      '👨‍👨‍👧‍👦', '👨‍👨‍👦‍👦', '👨‍👨‍👧‍👧', '👩‍👩‍👧', '👩‍👩‍👧‍👦', '👩‍👩‍👦‍👦', '👩‍👩‍👧‍👧', '🤝', '👋', '👏',
      '🙌', '👐', '🤲', '🙏', '✍️', '💪', '🦾', '🦿', '🦵', '🦶',
      '👂', '🦻', '👃', '🧠', '🫀', '🫁', '🦷', '🦴', '👀', '👁️',
      '👅', '👄', '💋', '🩸', '🎉', '🎊', '🎈', '🎁', '🎀', '🎗️',
      '🎟️', '🎫', '🎖️', '🏆', '🏅', '🥇', '🥈', '🥉', '⚽', '🏀'
    ],
    'Genel': [
      '📍', '📌', '📎', '🔗', '⭐', '🌟', '✨', '💫', '⚡', '🔥',
      '💧', '🌊', '❄️', '☀️', '🌤️', '⛅', '🌦️', '🌧️', '⛈️', '🌩️',
      '🌨️', '☁️', '🌪️', '🌈', '☂️', '☔', '⛱️', '⚡', '❄️', '☃️',
      '⛄', '☄️', '🔥', '💧', '🌊', '💎', '💰', '💳', '💸', '💵',
      '💴', '💶', '💷', '🪙', '💲', '💱', '💹', '📊', '📈', '📉'
    ]
  };

  List<String> get _filteredIcons {
    if (_searchQuery.isEmpty) {
      return _iconCategories.values.expand((icons) => icons).toList();
    }
    
    // Basit arama - emoji'nin kategorisine göre
    List<String> filtered = [];
    _iconCategories.forEach((category, icons) {
      if (category.toLowerCase().contains(_searchQuery.toLowerCase())) {
        filtered.addAll(icons);
      }
    });
    return filtered;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 300,
      height: 400,
      child: Column(
        children: [
          // Başlık
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Text(
                  'İkon Seç',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  widget.currentIcon,
                  style: const TextStyle(fontSize: 24),
                ),
              ],
            ),
          ),
          
          // Arama çubuğu
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Kategori ara (yaşam, göç, iş...)',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          
          // İkon grid'i
          Expanded(
            child: GridView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 6,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: _filteredIcons.length,
              itemBuilder: (context, index) {
                final icon = _filteredIcons[index];
                final isSelected = icon == widget.currentIcon;
                
                return GestureDetector(
                  onTap: () {
                    widget.onIconSelected(icon);
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.blue.withOpacity(0.2) : Colors.grey.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: isSelected 
                          ? Border.all(color: Colors.blue, width: 2)
                          : Border.all(color: Colors.grey.withOpacity(0.3)),
                    ),
                    child: Center(
                      child: Text(
                        icon,
                        style: const TextStyle(fontSize: 24),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          
          // Alt butonlar
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('İptal'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      // Manuel giriş için dialog
                      _showManualInputDialog(context);
                    },
                    child: const Text('Manuel Gir'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showManualInputDialog(BuildContext context) {
    final controller = TextEditingController(text: widget.currentIcon);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('İkon Manuel Gir'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            hintText: 'Emoji veya ikon girin',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('İptal'),
          ),
          ElevatedButton(
            onPressed: () {
              widget.onIconSelected(controller.text);
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('Tamam'),
          ),
        ],
      ),
    );
  }
}
