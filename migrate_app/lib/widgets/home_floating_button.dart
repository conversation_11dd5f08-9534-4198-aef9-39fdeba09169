import 'package:flutter/material.dart';
import '../screens/home_screen.dart';

class HomeFloatingButton extends StatelessWidget {
  final bool show;
  
  const HomeFloatingButton({
    Key? key,
    this.show = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!show) return const SizedBox.shrink();
    
    return FloatingActionButton(
      onPressed: () {
        // Ana sayfaya git
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const HomeScreen()),
          (route) => false,
        );
      },
      backgroundColor: Colors.blue[600],
      foregroundColor: Colors.white,
      tooltip: '<PERSON>',
      child: const Icon(Icons.home),
    );
  }
}
