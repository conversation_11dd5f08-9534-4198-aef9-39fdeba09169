import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../utils/platform_utils.dart';

/// Platform-aware Scaffold
class PlatformScaffold extends StatelessWidget {
  final Widget body;
  final PreferredSizeWidget? appBar;
  final Widget? navigationBar;
  final Widget? bottomNavigationBar;
  final Color? backgroundColor;
  final Widget? drawer;
  final Widget? floatingActionButton;

  const PlatformScaffold({
    Key? key,
    required this.body,
    this.appBar,
    this.navigationBar,
    this.bottomNavigationBar,
    this.backgroundColor,
    this.drawer,
    this.floatingActionButton,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (PlatformUtils.isIOS) {
      return CupertinoPageScaffold(
        navigationBar: navigationBar as CupertinoNavigationBar?,
        backgroundColor: backgroundColor ?? CupertinoColors.systemBackground,
        child: body,
      );
    } else {
      return Scaffold(
        appBar: appBar,
        body: body,
        bottomNavigationBar: bottomNavigationBar,
        backgroundColor: backgroundColor,
        drawer: drawer,
        floatingActionButton: floatingActionButton,
      );
    }
  }
}

/// Platform-aware Button
class PlatformButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final Color? color;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final bool isDestructive;

  const PlatformButton({
    Key? key,
    required this.child,
    this.onPressed,
    this.color,
    this.padding,
    this.borderRadius,
    this.isDestructive = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (PlatformUtils.isIOS) {
      return CupertinoButton(
        onPressed: onPressed != null
            ? () {
                PlatformUtils.hapticFeedback(type: HapticFeedbackType.light);
                onPressed!();
              }
            : null,
        color: color ?? (isDestructive ? CupertinoColors.destructiveRed : CupertinoColors.activeBlue),
        padding: padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        borderRadius: borderRadius ?? BorderRadius.circular(8),
        child: child,
      );
    } else {
      return ElevatedButton(
        onPressed: onPressed != null
            ? () {
                PlatformUtils.hapticFeedback(type: HapticFeedbackType.light);
                onPressed!();
              }
            : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: color ?? (isDestructive ? Colors.red : Theme.of(context).primaryColor),
          padding: padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius ?? BorderRadius.circular(8),
          ),
        ),
        child: child,
      );
    }
  }
}

/// Platform-aware Text Button
class PlatformTextButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final Color? color;

  const PlatformTextButton({
    Key? key,
    required this.child,
    this.onPressed,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (PlatformUtils.isIOS) {
      return CupertinoButton(
        onPressed: onPressed != null
            ? () {
                PlatformUtils.hapticFeedback(type: HapticFeedbackType.selection);
                onPressed!();
              }
            : null,
        padding: EdgeInsets.zero,
        child: DefaultTextStyle(
          style: TextStyle(
            color: color ?? CupertinoColors.activeBlue,
            fontSize: 16,
          ),
          child: child,
        ),
      );
    } else {
      return TextButton(
        onPressed: onPressed != null
            ? () {
                PlatformUtils.hapticFeedback(type: HapticFeedbackType.selection);
                onPressed!();
              }
            : null,
        child: DefaultTextStyle(
          style: TextStyle(
            color: color ?? Theme.of(context).primaryColor,
            fontSize: 16,
          ),
          child: child,
        ),
      );
    }
  }
}

/// Platform-aware TextField
class PlatformTextField extends StatelessWidget {
  final TextEditingController? controller;
  final String? placeholder;
  final String? labelText;
  final bool obscureText;
  final TextInputType? keyboardType;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onTap;
  final bool readOnly;
  final Widget? prefix;
  final Widget? suffix;
  final int? maxLines;
  final EdgeInsetsGeometry? padding;

  const PlatformTextField({
    Key? key,
    this.controller,
    this.placeholder,
    this.labelText,
    this.obscureText = false,
    this.keyboardType,
    this.onChanged,
    this.onTap,
    this.readOnly = false,
    this.prefix,
    this.suffix,
    this.maxLines = 1,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (PlatformUtils.isIOS) {
      return CupertinoTextField(
        controller: controller,
        placeholder: placeholder ?? labelText,
        obscureText: obscureText,
        keyboardType: keyboardType,
        onChanged: onChanged,
        onTap: onTap,
        readOnly: readOnly,
        prefix: prefix,
        suffix: suffix,
        maxLines: maxLines,
        padding: padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: CupertinoColors.tertiarySystemFill,
          borderRadius: BorderRadius.circular(8),
        ),
      );
    } else {
      return TextField(
        controller: controller,
        obscureText: obscureText,
        keyboardType: keyboardType,
        onChanged: onChanged,
        onTap: onTap,
        readOnly: readOnly,
        maxLines: maxLines,
        decoration: InputDecoration(
          labelText: labelText,
          hintText: placeholder,
          prefixIcon: prefix,
          suffixIcon: suffix,
          filled: true,
          fillColor: Colors.grey[100],
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide.none,
          ),
          contentPadding: padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
      );
    }
  }
}

/// Platform-aware Card
class PlatformCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final Color? color;
  final BorderRadius? borderRadius;
  final VoidCallback? onTap;

  const PlatformCard({
    Key? key,
    required this.child,
    this.margin,
    this.padding,
    this.color,
    this.borderRadius,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final cardChild = Container(
      margin: margin,
      padding: padding ?? const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color ?? PlatformUtils.getCardColor(context),
        borderRadius: borderRadius ?? BorderRadius.circular(PlatformUtils.isIOS ? 12 : 8),
        boxShadow: PlatformUtils.isIOS
            ? []
            : [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
      ),
      child: child,
    );

    if (onTap != null) {
      if (PlatformUtils.isIOS) {
        return GestureDetector(
          onTap: () {
            PlatformUtils.hapticFeedback(type: HapticFeedbackType.light);
            onTap!();
          },
          child: cardChild,
        );
      } else {
        return InkWell(
          onTap: () {
            PlatformUtils.hapticFeedback(type: HapticFeedbackType.light);
            onTap!();
          },
          borderRadius: borderRadius ?? BorderRadius.circular(8),
          child: cardChild,
        );
      }
    }

    return cardChild;
  }
}
