import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'dart:typed_data';
import 'dart:ui' as ui;

class ProfilePhotoCropDialog extends StatefulWidget {
  final Uint8List imageBytes;
  final String fileName;
  final Function(Uint8List croppedBytes) onCropped;

  const ProfilePhotoCropDialog({
    Key? key,
    required this.imageBytes,
    required this.fileName,
    required this.onCropped,
  }) : super(key: key);

  @override
  State<ProfilePhotoCropDialog> createState() => _ProfilePhotoCropDialogState();
}

class _ProfilePhotoCropDialogState extends State<ProfilePhotoCropDialog> {
  bool _isProcessing = false;
  double _scale = 1.0;
  double _previousScale = 1.0;
  Offset _offset = Offset.zero;
  Offset _previousOffset = Offset.zero;
  late ui.Image _image;
  bool _imageLoaded = false;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  Future<void> _loadImage() async {
    final codec = await ui.instantiateImageCodec(widget.imageBytes);
    final frame = await codec.getNextFrame();
    setState(() {
      _image = frame.image;
      _imageLoaded = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 500,
        height: 600,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.crop, color: Colors.white),
                  const SizedBox(width: 8),
                  const Text(
                    'Profil Fotoğrafını Düzenle',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
            ),

            // Crop Area
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    const Text(
                      'Fotoğrafınızı daire şeklinde kırpın',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Crop Preview
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: _buildCropPreview(),
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Instructions
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.info_outline, color: Colors.blue.shade700),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Fotoğrafınız profil resmi olarak daire şeklinde görünecektir.',
                              style: TextStyle(
                                color: Colors.blue.shade700,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Actions
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isProcessing ? null : () => Navigator.pop(context),
                      child: const Text('İptal'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isProcessing ? null : _cropAndSave,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                      child: _isProcessing
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                          : const Text('Kaydet'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCropPreview() {
    if (!_imageLoaded) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        final cropSize = constraints.maxWidth * 0.6;

        return GestureDetector(
          onScaleStart: (details) {
            _previousScale = _scale;
            _previousOffset = _offset;
          },
          onScaleUpdate: (details) {
            setState(() {
              _scale = (_previousScale * details.scale).clamp(0.5, 3.0);

              final delta = details.focalPoint - details.localFocalPoint;
              _offset = _previousOffset + delta;

              // Sınırları kontrol et
              final maxOffset = constraints.maxWidth * 0.3;
              _offset = Offset(
                _offset.dx.clamp(-maxOffset, maxOffset),
                _offset.dy.clamp(-maxOffset, maxOffset),
              );
            });
          },
          child: Stack(
            children: [
              // Background (karartılmış alan)
              Container(
                width: constraints.maxWidth,
                height: constraints.maxHeight,
                color: Colors.black.withOpacity(0.7),
              ),

              // Hareket ettirilebilir fotoğraf
              Positioned.fill(
                child: Transform.translate(
                  offset: _offset,
                  child: Transform.scale(
                    scale: _scale,
                    child: Image.memory(
                      widget.imageBytes,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),

              // Daire crop alanı
              Center(
                child: Container(
                  width: cropSize,
                  height: cropSize,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white,
                      width: 3,
                    ),
                  ),
                  child: ClipOval(
                    child: Transform.translate(
                      offset: _offset,
                      child: Transform.scale(
                        scale: _scale,
                        child: Image.memory(
                          widget.imageBytes,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              // Zoom/Pan ipuçları
              Positioned(
                bottom: 16,
                left: 16,
                right: 16,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    'Fotoğrafı hareket ettirin ve zoom yapın',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _cropAndSave() async {
    setState(() {
      _isProcessing = true;
    });

    try {
      // Crop işlemi için canvas kullan
      final croppedBytes = await _cropImageToCircle();
      widget.onCropped(croppedBytes);

      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fotoğraf işleme hatası: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  Future<Uint8List> _cropImageToCircle() async {
    const cropSize = 400; // Final crop boyutu

    // Canvas oluştur
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);

    // Daire clip path
    final path = Path()
      ..addOval(Rect.fromLTWH(0, 0, cropSize.toDouble(), cropSize.toDouble()));
    canvas.clipPath(path);

    // Fotoğrafı çiz (transform'larla)
    canvas.save();
    canvas.translate(cropSize / 2, cropSize / 2);
    canvas.scale(_scale);
    canvas.translate(_offset.dx, _offset.dy);
    canvas.translate(-_image.width / 2, -_image.height / 2);

    final paint = Paint();
    canvas.drawImage(_image, Offset.zero, paint);
    canvas.restore();

    // Picture'ı image'e çevir
    final picture = recorder.endRecording();
    final img = await picture.toImage(cropSize, cropSize);

    // ByteData'ya çevir
    final byteData = await img.toByteData(format: ui.ImageByteFormat.png);
    return byteData!.buffer.asUint8List();
  }
}

class GridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.3)
      ..strokeWidth = 1;

    // Vertical lines
    for (int i = 1; i < 3; i++) {
      final x = size.width * i / 3;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // Horizontal lines
    for (int i = 1; i < 3; i++) {
      final y = size.height * i / 3;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
