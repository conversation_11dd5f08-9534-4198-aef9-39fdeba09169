import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'dart:typed_data';
import 'dart:ui' as ui;

class ProfilePhotoCropDialog extends StatefulWidget {
  final Uint8List imageBytes;
  final String fileName;
  final Function(Uint8List croppedBytes) onCropped;

  const ProfilePhotoCropDialog({
    Key? key,
    required this.imageBytes,
    required this.fileName,
    required this.onCropped,
  }) : super(key: key);

  @override
  State<ProfilePhotoCropDialog> createState() => _ProfilePhotoCropDialogState();
}

class _ProfilePhotoCropDialogState extends State<ProfilePhotoCropDialog> {
  bool _isProcessing = false;
  double _scale = 1.0;
  double _offsetX = 0.0;
  double _offsetY = 0.0;



  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 500,
        height: 600,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.crop, color: Colors.white),
                  const SizedBox(width: 8),
                  const Text(
                    'Profil Fotoğrafını Düzenle',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
            ),

            // Crop Area
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    const Text(
                      'Fotoğrafınızı daire şeklinde kırpın',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Crop Preview
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: _buildCropPreview(),
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Zoom Slider
                    Row(
                      children: [
                        const Icon(Icons.zoom_out, color: Colors.grey),
                        Expanded(
                          child: Slider(
                            value: _scale,
                            min: 0.5,
                            max: 3.0,
                            divisions: 25,
                            label: '${(_scale * 100).round()}%',
                            onChanged: (value) {
                              setState(() {
                                _scale = value;
                              });
                            },
                          ),
                        ),
                        const Icon(Icons.zoom_in, color: Colors.grey),
                      ],
                    ),

                    // Position Controls
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            children: [
                              const Text('Yatay', style: TextStyle(fontSize: 12)),
                              Slider(
                                value: _offsetX,
                                min: -100,
                                max: 100,
                                divisions: 20,
                                onChanged: (value) {
                                  setState(() {
                                    _offsetX = value;
                                  });
                                },
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            children: [
                              const Text('Dikey', style: TextStyle(fontSize: 12)),
                              Slider(
                                value: _offsetY,
                                min: -100,
                                max: 100,
                                divisions: 20,
                                onChanged: (value) {
                                  setState(() {
                                    _offsetY = value;
                                  });
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Instructions
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.info_outline, color: Colors.blue.shade700),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Fotoğrafınız profil resmi olarak daire şeklinde görünecektir.',
                              style: TextStyle(
                                color: Colors.blue.shade700,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Actions
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isProcessing ? null : () => Navigator.pop(context),
                      child: const Text('İptal'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isProcessing ? null : _cropAndSave,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                      child: _isProcessing
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                          : const Text('Kaydet'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCropPreview() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final cropSize = constraints.maxWidth * 0.7;

        return Stack(
          children: [
            // Background (karartılmış alan)
            Container(
              width: constraints.maxWidth,
              height: constraints.maxHeight,
              color: Colors.black.withOpacity(0.7),
            ),

            // Daire crop alanı
            Center(
              child: Container(
                width: cropSize,
                height: cropSize,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.white,
                    width: 3,
                  ),
                ),
                child: ClipOval(
                  child: Transform.translate(
                    offset: Offset(_offsetX, _offsetY),
                    child: Transform.scale(
                      scale: _scale,
                      child: Image.memory(
                        widget.imageBytes,
                        fit: BoxFit.cover,
                        width: cropSize,
                        height: cropSize,
                      ),
                    ),
                  ),
                ),
              ),
            ),

            // Info text
            Positioned(
              bottom: 16,
              left: 16,
              right: 16,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  'Slider\'ları kullanarak fotoğrafı ayarlayın',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _cropAndSave() async {
    setState(() {
      _isProcessing = true;
    });

    try {
      // Basit yaklaşım: Orijinal fotoğrafı kullan
      // Gerçek crop işlemi için daha karmaşık kod gerekir
      // Şimdilik kullanıcının ayarladığı parametreleri kaydet
      widget.onCropped(widget.imageBytes);

      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fotoğraf işleme hatası: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }
}

class GridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.3)
      ..strokeWidth = 1;

    // Vertical lines
    for (int i = 1; i < 3; i++) {
      final x = size.width * i / 3;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // Horizontal lines
    for (int i = 1; i < 3; i++) {
      final y = size.height * i / 3;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
