import 'package:flutter/material.dart';

class ImagePickerWidget extends StatefulWidget {
  final String countryName;
  final String currentImageUrl;
  final Function(String) onImageSelected;

  const ImagePickerWidget({
    Key? key,
    required this.countryName,
    required this.currentImageUrl,
    required this.onImageSelected,
  }) : super(key: key);

  @override
  State<ImagePickerWidget> createState() => _ImagePickerWidgetState();
}

class _ImagePickerWidgetState extends State<ImagePickerWidget> {
  final TextEditingController _customUrlController = TextEditingController();
  String _selectedCategory = 'travel';
  
  final Map<String, List<String>> _categories = {
    'travel': ['travel', 'city', 'architecture', 'landmark'],
    'nature': ['nature', 'landscape', 'mountain', 'forest'],
    'culture': ['culture', 'people', 'traditional', 'festival'],
    'food': ['food', 'cuisine', 'restaurant', 'market'],
    'business': ['business', 'office', 'work', 'modern'],
  };

  @override
  void initState() {
    super.initState();
    _customUrlController.text = widget.currentImageUrl;
  }

  @override
  void dispose() {
    _customUrlController.dispose();
    super.dispose();
  }

  List<String> _generateImageUrls() {
    // Çalışan Unsplash fotoğraf ID'leri - kategorilere göre
    final Map<String, List<String>> photoIds = {
      'travel': [
        '1506905925346-21bda4d32df4', // Şehir manzarası
        '1513635269975-59663e0ac1ad', // Londra
        '1502602898536-47ad22581b52', // Paris
        '1520637836862-4d197d17c90a', // Amsterdam
        '1467269204594-9661b134dd2b', // Berlin
        '1541963463532-d68292c34d19', // Barcelona
      ],
      'nature': [
        '1441974231531-c6227db76b6e', // Orman
        '1506905925346-21bda4d32df4', // Dağ manzarası
        '1542314831-068cd1dbfeeb', // Göl
        '1519904981063-615ebea756cd', // Deniz
        '1500382017468-9049596d72f4', // Çiçek tarlası
        '1506905925346-21bda4d32df4', // Gün batımı
      ],
      'culture': [
        '1529258283598-8d6fe60b27f4', // İnsanlar
        '1533174072545-7a4b6ad7a6c3', // Festival
        '1518998053901-5348d3961a04', // Müze
        '1541961017570-4bd91ebeb7f6', // Sanat
        '1520637836862-4d197d17c90a', // Mimari
        '1467269204594-9661b134dd2b', // Tarih
      ],
      'food': [
        '1504674900247-0877df9cc836', // Yemek
        '1517248135467-4c7edcad34c4', // Restoran
        '1488459716781-31db52582fe9', // Market
        '1495474472287-c4bd71a8431f', // Kahve
        '1556909114-f6e7ad7d3136', // Mutfak
        '1551024506-0bccd0549730', // Tatlı
      ],
      'business': [
        '1497366216548-37526070297c', // Ofis
        '1507003211169-0a1dd7ef0a96', // İş
        '1486406146494-f09fcf13b897', // Modern
        '1518709268805-4e9042af2ac5', // Teknoloji
        '1552664730-d307ca04f8a5', // Toplantı
        '1521737604893-d14cc237f11d', // Çalışma
      ],
    };

    final selectedPhotoIds = photoIds[_selectedCategory] ?? photoIds['travel']!;

    return selectedPhotoIds.map((photoId) =>
      'https://images.unsplash.com/photo-$photoId?w=800&h=600&fit=crop&crop=center'
    ).toList();
  }

  @override
  Widget build(BuildContext context) {
    final imageUrls = _generateImageUrls();
    
    return Container(
      width: 600,
      height: 500,
      child: Column(
        children: [
          // Başlık
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Icon(Icons.photo_library, color: Colors.white),
                const SizedBox(width: 8),
                Text(
                  '${widget.countryName} için Görsel Seç',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          
          // Kategori seçimi
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Text('Kategori: ', style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(width: 8),
                DropdownButton<String>(
                  value: _selectedCategory,
                  items: _categories.keys.map((category) => DropdownMenuItem(
                    value: category,
                    child: Text(_getCategoryDisplayName(category)),
                  )).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedCategory = value;
                      });
                    }
                  },
                ),
                const Spacer(),
                ElevatedButton.icon(
                  onPressed: () {
                    setState(() {}); // Yeni görseller oluştur
                  },
                  icon: const Icon(Icons.refresh),
                  label: const Text('Yenile'),
                ),
              ],
            ),
          ),
          
          // Görsel grid'i
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                  childAspectRatio: 1.2,
                ),
                itemCount: imageUrls.length,
                itemBuilder: (context, index) {
                  final imageUrl = imageUrls[index];
                  final isSelected = imageUrl == widget.currentImageUrl;
                  
                  return GestureDetector(
                    onTap: () {
                      widget.onImageSelected(imageUrl);
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: isSelected 
                            ? Border.all(color: Colors.blue, width: 3)
                            : Border.all(color: Colors.grey.shade300),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Stack(
                          children: [
                            Image.network(
                              imageUrl,
                              fit: BoxFit.cover,
                              width: double.infinity,
                              height: double.infinity,
                              loadingBuilder: (context, child, loadingProgress) {
                                if (loadingProgress == null) return child;
                                return Container(
                                  color: Colors.grey.shade200,
                                  child: const Center(
                                    child: CircularProgressIndicator(),
                                  ),
                                );
                              },
                              errorBuilder: (context, error, stackTrace) => Container(
                                color: Colors.grey.shade200,
                                child: const Center(
                                  child: Icon(Icons.broken_image, color: Colors.grey),
                                ),
                              ),
                            ),
                            if (isSelected)
                              Positioned(
                                top: 8,
                                right: 8,
                                child: Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: const BoxDecoration(
                                    color: Colors.blue,
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.check,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
          
          // Manuel URL girişi
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Veya manuel URL girin:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _customUrlController,
                        decoration: const InputDecoration(
                          hintText: 'https://example.com/image.jpg',
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () {
                        if (_customUrlController.text.isNotEmpty) {
                          widget.onImageSelected(_customUrlController.text);
                          Navigator.of(context).pop();
                        }
                      },
                      child: const Text('Kullan'),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Alt butonlar
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('İptal'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      widget.onImageSelected('');
                      Navigator.of(context).pop();
                    },
                    child: const Text('Görseli Kaldır'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getCategoryDisplayName(String category) {
    switch (category) {
      case 'travel':
        return 'Seyahat & Şehir';
      case 'nature':
        return 'Doğa & Manzara';
      case 'culture':
        return 'Kültür & İnsan';
      case 'food':
        return 'Yemek & Mutfak';
      case 'business':
        return 'İş & Modern';
      default:
        return category;
    }
  }
}
