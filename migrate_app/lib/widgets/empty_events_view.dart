import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../screens/create_event_screen.dart';

/// Etkinlik listesi boş olduğunda gösterilecek widget
class EmptyEventsView extends StatelessWidget {
  final AppUser? currentUser;
  final VoidCallback onRefresh;

  const EmptyEventsView({
    Key? key,
    required this.currentUser,
    required this.onRefresh,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bool isAdmin = currentUser?.isAdmin ?? false;
    final String cityName = currentUser?.city ?? '';

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // İllüstrasyon
            Icon(
              Icons.event_busy,
              size: 100,
              color: Colors.grey[400],
            ),
            
            const SizedBox(height: 24),
            
            // Başlık
            Text(
              'Henüz etkinlik yok 😔',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 16),
            
            // Açıklama
            Text(
              cityName.isNotEmpty
                  ? '$cityName şehrinde henüz bir etkinlik oluşturulmadı. ${isAdmin ? 'Yeni bir etkinlik oluşturarak topluluğu başlatabilirsin!' : 'Etkinlikler oluşturulduğunda burada görünecek.'}'
                  : 'Henüz kimse bir etkinlik oluşturmadı. ${isAdmin ? 'Yeni bir etkinlik oluşturarak topluluğu başlatabilirsin!' : 'Etkinlikler oluşturulduğunda burada görünecek.'}',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 32),
            
            // Buton
            if (isAdmin)
              ElevatedButton.icon(
                onPressed: () async {
                  final result = await Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const CreateEventScreen(),
                    ),
                  );
                  
                  if (result == true) {
                    onRefresh();
                  }
                },
                icon: const Icon(Icons.add),
                label: const Text(
                  'Etkinlik Oluştur',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              )
            else
              ElevatedButton.icon(
                onPressed: onRefresh,
                icon: const Icon(Icons.refresh),
                label: const Text(
                  'Sayfayı Yenile',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
