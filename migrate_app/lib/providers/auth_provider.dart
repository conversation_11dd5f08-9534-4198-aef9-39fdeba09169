import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/auth_service.dart';

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();
  User? _user;
  bool _isLoading = false;
  String? _errorMessage;

  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isLoggedIn => _user != null;

  AuthProvider() {
    _initializeAuth();
  }

  void _initializeAuth() {
    _user = _authService.currentUser;

    // Auth state değişikliklerini dinle
    Supabase.instance.client.auth.onAuthStateChange.listen((AuthState state) {
      _user = state.session?.user;
      notifyListeners();
    });
  }

  // Kayıt ol
  Future<bool> signUp({
    required String email,
    required String password,
    required String fullName,
    required String city,
    required BuildContext context,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      await _authService.signUp(
        email: email,
        password: password,
        city: city,
        country: 'Türkiye',
        context: context,
      );

      _user = _authService.currentUser;
      _setLoading(false);
      return true;
    } catch (e) {
      _setError(_getErrorMessage(e));
      _setLoading(false);
      return false;
    }
  }

  // Giriş yap
  Future<bool> signIn({
    required String email,
    required String password,
  }) async {
    print('AuthProvider: Giriş işlemi başlatılıyor - Email: $email');
    _setLoading(true);
    _clearError();

    try {
      await _authService.signIn(
        email: email,
        password: password,
      );

      _user = _authService.currentUser;
      print('AuthProvider: Giriş başarılı - User ID: ${_user?.id}');
      _setLoading(false);
      return true;
    } catch (e) {
      print('AuthProvider: Giriş hatası - $e');
      _setError(_getErrorMessage(e));
      _setLoading(false);
      return false;
    }
  }

  // Şifre sıfırlama
  Future<bool> resetPassword(String email) async {
    _setLoading(true);
    _clearError();

    try {
      await _authService.resetPassword(email: email);
      _setLoading(false);
      return true;
    } catch (e) {
      _setError(_getErrorMessage(e));
      _setLoading(false);
      return false;
    }
  }

  // Çıkış yap
  Future<void> signOut() async {
    _setLoading(true);
    try {
      await _authService.signOut();
      _user = null;
    } catch (e) {
      _setError(_getErrorMessage(e));
    }
    _setLoading(false);
  }

  // Profil güncelle
  Future<bool> updateProfile({
    String? fullName,
    String? city,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      await _authService.updatePassword(newPassword: 'temp'); // Placeholder
      _user = _authService.currentUser;
      _setLoading(false);
      return true;
    } catch (e) {
      _setError(_getErrorMessage(e));
      _setLoading(false);
      return false;
    }
  }

  // Kullanıcı bilgilerini al
  Map<String, dynamic>? getUserData() {
    final user = _authService.currentUser;
    if (user == null) return null;

    return {
      'id': user.id,
      'email': user.email,
      'created_at': user.createdAt,
    };
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  String _getErrorMessage(dynamic error) {
    if (error is AuthException) {
      switch (error.message) {
        case 'Invalid login credentials':
          return 'Geçersiz email veya şifre';
        case 'Email not confirmed':
          return 'Email adresinizi doğrulayın';
        case 'User already registered':
          return 'Bu email adresi zaten kayıtlı';
        case 'Password should be at least 6 characters':
          return 'Şifre en az 6 karakter olmalı';
        default:
          return error.message;
      }
    }
    return error.toString();
  }
}
