import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:typed_data';
import '../models/user_profile_model.dart';
import '../services/user_profile_service.dart';
import '../services/storage_service.dart';
import '../widgets/responsive_container.dart';

class ProfileEditScreen extends StatefulWidget {
  final UserProfile? existingProfile;
  
  const ProfileEditScreen({
    Key? key,
    this.existingProfile,
  }) : super(key: key);

  @override
  State<ProfileEditScreen> createState() => _ProfileEditScreenState();
}

class _ProfileEditScreenState extends State<ProfileEditScreen> {
  final UserProfileService _profileService = UserProfileService();
  final StorageService _storageService = StorageService();
  final _formKey = GlobalKey<FormState>();
  
  // Form controllers
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _bioController = TextEditingController();
  final _professionController = TextEditingController();
  final _industryController = TextEditingController();
  final _currentCompanyController = TextEditingController();
  final _currentPositionController = TextEditingController();
  final _linkedinController = TextEditingController();
  final _websiteController = TextEditingController();
  final _avatarController = TextEditingController();
  
  // Dropdown values
  String _fromCountry = '';
  String _fromCity = '';
  String _toCountry = '';
  String _toCity = '';
  String _migrationStatus = 'planning';
  String _experienceLevel = 'mid';
  
  // Multi-select values
  List<String> _selectedSkills = [];
  List<String> _selectedHelpTopics = [];
  List<String> _selectedUserCategories = [];
  List<String> _selectedNetworkingGoals = [];
  List<String> _selectedLanguages = ['tr'];
  
  // Switches
  bool _isOpenToMentoring = false;
  bool _isSeekingMentor = false;
  bool _isOpenToNetworking = true;
  bool _isPublic = true;
  
  bool _isLoading = false;
  bool _isUploadingPhoto = false;

  @override
  void initState() {
    super.initState();
    _loadExistingProfile();
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _bioController.dispose();
    _professionController.dispose();
    _industryController.dispose();
    _currentCompanyController.dispose();
    _currentPositionController.dispose();
    _linkedinController.dispose();
    _websiteController.dispose();
    _avatarController.dispose();
    super.dispose();
  }

  void _loadExistingProfile() {
    if (widget.existingProfile != null) {
      final profile = widget.existingProfile!;
      _firstNameController.text = profile.firstName;
      _lastNameController.text = profile.lastName;
      _bioController.text = profile.bio ?? '';
      _professionController.text = profile.profession;
      _industryController.text = profile.industry;
      _currentCompanyController.text = profile.currentCompany ?? '';
      _currentPositionController.text = profile.currentPosition ?? '';
      _linkedinController.text = profile.linkedinUrl ?? '';
      _websiteController.text = profile.websiteUrl ?? '';
      _avatarController.text = profile.avatar ?? '';
      
      _fromCountry = profile.fromCountry;
      _fromCity = profile.fromCity;
      _toCountry = profile.toCountry;
      _toCity = profile.toCity;
      _migrationStatus = profile.migrationStatus;
      _experienceLevel = profile.experienceLevel;
      
      _selectedSkills = List.from(profile.skills);
      _selectedHelpTopics = List.from(profile.helpTopics);
      _selectedUserCategories = List.from(profile.userCategories);
      _selectedNetworkingGoals = List.from(profile.networkingGoals);
      _selectedLanguages = List.from(profile.preferredLanguages);
      
      _isOpenToMentoring = profile.isOpenToMentoring;
      _isSeekingMentor = profile.isSeekingMentor;
      _isOpenToNetworking = profile.isOpenToNetworking;
      _isPublic = profile.isPublic;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        title: Text(
          widget.existingProfile != null ? 'Profili Düzenle' : 'Profil Oluştur',
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: ElevatedButton(
              onPressed: _isLoading ? null : _saveProfile,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : const Text('Kaydet'),
            ),
          ),
        ],
      ),
      body: ResponsiveContainer(
        maxWidth: 800,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Kişisel Bilgiler
                _buildPersonalInfoSection(),
                
                const SizedBox(height: 20),
                
                // Göç Bilgileri
                _buildMigrationInfoSection(),
                
                const SizedBox(height: 20),
                
                // Profesyonel Bilgiler
                _buildProfessionalInfoSection(),
                
                const SizedBox(height: 20),
                
                // Networking Tercihleri
                _buildNetworkingSection(),
                
                const SizedBox(height: 20),
                
                // İletişim Tercihleri
                _buildCommunicationSection(),
                
                const SizedBox(height: 20),
                
                // Sosyal Medya
                _buildSocialMediaSection(),
                
                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPersonalInfoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Kişisel Bilgiler',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Profil Fotoğrafı
          _buildProfilePhotoSection(),

          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _firstNameController,
                  decoration: const InputDecoration(
                    labelText: 'Ad *',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Ad gerekli';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: TextFormField(
                  controller: _lastNameController,
                  decoration: const InputDecoration(
                    labelText: 'Soyad *',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Soyad gerekli';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _bioController,
            maxLines: 3,
            decoration: const InputDecoration(
              labelText: 'Hakkında',
              hintText: 'Kendinizi kısaca tanıtın...',
              border: OutlineInputBorder(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMigrationInfoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Göç Bilgileri',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _fromCountry.isEmpty ? null : _fromCountry,
                  decoration: const InputDecoration(
                    labelText: 'Nereden *',
                    border: OutlineInputBorder(),
                  ),
                  items: _getCountryItems(),
                  onChanged: (value) {
                    setState(() {
                      _fromCountry = value ?? '';
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Ülke seçin';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _toCountry.isEmpty ? null : _toCountry,
                  decoration: const InputDecoration(
                    labelText: 'Nereye *',
                    border: OutlineInputBorder(),
                  ),
                  items: _getCountryItems(),
                  onChanged: (value) {
                    setState(() {
                      _toCountry = value ?? '';
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Ülke seçin';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _migrationStatus,
            decoration: const InputDecoration(
              labelText: 'Göç Durumu',
              border: OutlineInputBorder(),
            ),
            items: const [
              DropdownMenuItem(value: 'planning', child: Text('Planlama Aşamasında')),
              DropdownMenuItem(value: 'in-progress', child: Text('Süreç Devam Ediyor')),
              DropdownMenuItem(value: 'completed', child: Text('Tamamlandı')),
            ],
            onChanged: (value) {
              setState(() {
                _migrationStatus = value ?? 'planning';
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildProfessionalInfoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Profesyonel Bilgiler',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _professionController,
            decoration: const InputDecoration(
              labelText: 'Meslek *',
              hintText: 'Yazılım Geliştirici',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Meslek gerekli';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _industryController,
                  decoration: const InputDecoration(
                    labelText: 'Sektör',
                    hintText: 'Teknoloji',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _experienceLevel,
                  decoration: const InputDecoration(
                    labelText: 'Deneyim Seviyesi',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'junior', child: Text('Junior')),
                    DropdownMenuItem(value: 'mid', child: Text('Mid-Level')),
                    DropdownMenuItem(value: 'senior', child: Text('Senior')),
                    DropdownMenuItem(value: 'expert', child: Text('Expert')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _experienceLevel = value ?? 'mid';
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _currentCompanyController,
                  decoration: const InputDecoration(
                    labelText: 'Mevcut Şirket',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: TextFormField(
                  controller: _currentPositionController,
                  decoration: const InputDecoration(
                    labelText: 'Mevcut Pozisyon',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNetworkingSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Networking Tercihleri',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'Hangi konularda yardım edebilirsiniz?',
            style: TextStyle(fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 8),
          _buildMultiSelectChips(
            options: ['vize', 'konut', 'iş', 'eğitim', 'sağlık', 'dil', 'kültür', 'finans'],
            selectedValues: _selectedHelpTopics,
            onChanged: (values) {
              setState(() {
                _selectedHelpTopics = values;
              });
            },
          ),
          const SizedBox(height: 16),
          const Text(
            'Kullanıcı kategoriniz?',
            style: TextStyle(fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 8),
          _buildMultiSelectChips(
            options: ['yeni-göçmen', 'deneyimli', 'profesyonel', 'sosyal', 'öğrenci', 'girişimci'],
            selectedValues: _selectedUserCategories,
            onChanged: (values) {
              setState(() {
                _selectedUserCategories = values;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCommunicationSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'İletişim Tercihleri',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          SwitchListTile(
            title: const Text('Mentoring yapmaya açığım'),
            subtitle: const Text('Yeni göçmenlere yardım etmek istiyorum'),
            value: _isOpenToMentoring,
            onChanged: (value) {
              setState(() {
                _isOpenToMentoring = value;
              });
            },
          ),
          SwitchListTile(
            title: const Text('Mentor arıyorum'),
            subtitle: const Text('Deneyimli birinden yardım almak istiyorum'),
            value: _isSeekingMentor,
            onChanged: (value) {
              setState(() {
                _isSeekingMentor = value;
              });
            },
          ),
          SwitchListTile(
            title: const Text('Networking\'e açığım'),
            subtitle: const Text('Yeni insanlarla tanışmak istiyorum'),
            value: _isOpenToNetworking,
            onChanged: (value) {
              setState(() {
                _isOpenToNetworking = value;
              });
            },
          ),
          SwitchListTile(
            title: const Text('Profilim herkese açık'),
            subtitle: const Text('Diğer kullanıcılar profilimi görebilir'),
            value: _isPublic,
            onChanged: (value) {
              setState(() {
                _isPublic = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSocialMediaSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Sosyal Medya & İletişim',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _linkedinController,
            decoration: const InputDecoration(
              labelText: 'LinkedIn URL',
              hintText: 'https://linkedin.com/in/kullanici-adi',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.work),
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _websiteController,
            decoration: const InputDecoration(
              labelText: 'Website URL',
              hintText: 'https://website.com',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.language),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMultiSelectChips({
    required List<String> options,
    required List<String> selectedValues,
    required Function(List<String>) onChanged,
  }) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: options.map((option) {
        final isSelected = selectedValues.contains(option);
        return FilterChip(
          label: Text(option),
          selected: isSelected,
          onSelected: (selected) {
            final newValues = List<String>.from(selectedValues);
            if (selected) {
              newValues.add(option);
            } else {
              newValues.remove(option);
            }
            onChanged(newValues);
          },
          selectedColor: Colors.blue.withOpacity(0.2),
          checkmarkColor: Colors.blue,
        );
      }).toList(),
    );
  }

  List<DropdownMenuItem<String>> _getCountryItems() {
    final countries = [
      'Türkiye', 'Hollanda', 'Almanya', 'Fransa', 'İngiltere', 
      'İspanya', 'İtalya', 'Belçika', 'Avusturya', 'İsviçre',
      'Kanada', 'ABD', 'Avustralya', 'Yeni Zelanda', 'Japonya'
    ];
    
    return countries.map((country) {
      return DropdownMenuItem(
        value: country,
        child: Text(country),
      );
    }).toList();
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      UserProfile profile;
      
      if (widget.existingProfile != null) {
        // Profil güncelle
        profile = await _profileService.updateProfile(
          firstName: _firstNameController.text.trim(),
          lastName: _lastNameController.text.trim(),
          bio: _bioController.text.trim().isEmpty ? null : _bioController.text.trim(),
          avatar: _avatarController.text.trim().isEmpty ? null : _avatarController.text.trim(),
          fromCountry: _fromCountry,
          toCountry: _toCountry,
          migrationStatus: _migrationStatus,
          profession: _professionController.text.trim(),
          industry: _industryController.text.trim(),
          experienceLevel: _experienceLevel,
          currentCompany: _currentCompanyController.text.trim().isEmpty ? null : _currentCompanyController.text.trim(),
          currentPosition: _currentPositionController.text.trim().isEmpty ? null : _currentPositionController.text.trim(),
          helpTopics: _selectedHelpTopics,
          userCategories: _selectedUserCategories,
          networkingGoals: _selectedNetworkingGoals,
          isOpenToMentoring: _isOpenToMentoring,
          isSeekingMentor: _isSeekingMentor,
          isOpenToNetworking: _isOpenToNetworking,
          preferredLanguages: _selectedLanguages,
          linkedinUrl: _linkedinController.text.trim().isEmpty ? null : _linkedinController.text.trim(),
          websiteUrl: _websiteController.text.trim().isEmpty ? null : _websiteController.text.trim(),
          isPublic: _isPublic,
        );
      } else {
        // Yeni profil oluştur
        profile = await _profileService.createProfile(
          firstName: _firstNameController.text.trim(),
          lastName: _lastNameController.text.trim(),
          bio: _bioController.text.trim().isEmpty ? null : _bioController.text.trim(),
          avatar: _avatarController.text.trim().isEmpty ? null : _avatarController.text.trim(),
          fromCountry: _fromCountry,
          fromCity: _fromCity,
          toCountry: _toCountry,
          toCity: _toCity,
          migrationStatus: _migrationStatus,
          profession: _professionController.text.trim(),
          industry: _industryController.text.trim(),
          experienceLevel: _experienceLevel,
          currentCompany: _currentCompanyController.text.trim().isEmpty ? null : _currentCompanyController.text.trim(),
          currentPosition: _currentPositionController.text.trim().isEmpty ? null : _currentPositionController.text.trim(),
          helpTopics: _selectedHelpTopics,
          userCategories: _selectedUserCategories,
          networkingGoals: _selectedNetworkingGoals,
          isOpenToMentoring: _isOpenToMentoring,
          isSeekingMentor: _isSeekingMentor,
          isOpenToNetworking: _isOpenToNetworking,
          preferredLanguages: _selectedLanguages,
          linkedinUrl: _linkedinController.text.trim().isEmpty ? null : _linkedinController.text.trim(),
          websiteUrl: _websiteController.text.trim().isEmpty ? null : _websiteController.text.trim(),
        );
      }
      
      if (mounted) {
        Navigator.pop(context, profile);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profil başarıyla kaydedildi!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Hata: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Widget _buildProfilePhotoSection() {
    return Column(
      children: [
        // Profil fotoğrafı önizleme
        Center(
          child: Stack(
            children: [
              CircleAvatar(
                radius: 50,
                backgroundColor: Colors.grey[200],
                backgroundImage: _avatarController.text.isNotEmpty
                    ? NetworkImage(_avatarController.text)
                    : null,
                child: _avatarController.text.isEmpty
                    ? Icon(
                        Icons.person,
                        size: 50,
                        color: Colors.grey[400],
                      )
                    : null,
              ),
              Positioned(
                bottom: 0,
                right: 0,
                child: GestureDetector(
                  onTap: _isUploadingPhoto ? null : _showPhotoOptions,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _isUploadingPhoto ? Colors.grey : Colors.blue,
                      shape: BoxShape.circle,
                    ),
                    child: _isUploadingPhoto
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : const Icon(
                            Icons.camera_alt,
                            color: Colors.white,
                            size: 20,
                          ),
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Kamera ikonuna tıklayarak fotoğraf ekleyin',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  void _showPhotoOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        return SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Handle bar
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 16),

                const Text(
                  'Profil Fotoğrafı',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),

                // Modern butonlar
                Row(
                  children: [
                    Expanded(
                      child: _buildPhotoOptionButton(
                        icon: Icons.photo_library,
                        label: 'Galeri',
                        onTap: () {
                          Navigator.pop(context);
                          _pickImageFromGallery();
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildPhotoOptionButton(
                        icon: Icons.camera_alt,
                        label: 'Kamera',
                        onTap: () {
                          Navigator.pop(context);
                          _pickImageFromCamera();
                        },
                      ),
                    ),
                  ],
                ),

                if (_avatarController.text.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      onPressed: () {
                        Navigator.pop(context);
                        _removePhoto();
                      },
                      icon: const Icon(Icons.delete, color: Colors.red),
                      label: const Text(
                        'Fotoğrafı Kaldır',
                        style: TextStyle(color: Colors.red),
                      ),
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: Colors.red),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],

                const SizedBox(height: 16),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPhotoOptionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: Colors.blue,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImageFromGallery() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (image != null) {
        // Dosyayı byte array'e çevir
        final Uint8List imageBytes = await image.readAsBytes();

        // Direkt yükle
        _uploadCroppedPhoto(imageBytes);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fotoğraf seçme hatası: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _uploadCroppedPhoto(Uint8List croppedBytes) async {
    try {
      setState(() {
        _isUploadingPhoto = true;
      });

      // Supabase Storage'a yükle
      final imageUrl = await _storageService.uploadProfilePhoto(
        croppedBytes,
        'profile_${DateTime.now().millisecondsSinceEpoch}.jpg',
      );

      // Eski fotoğrafı sil (varsa)
      if (_avatarController.text.isNotEmpty) {
        await _storageService.deleteProfilePhoto(_avatarController.text);
      }

      // URL'i güncelle
      setState(() {
        _avatarController.text = imageUrl;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profil fotoğrafı başarıyla yüklendi!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fotoğraf yükleme hatası: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploadingPhoto = false;
        });
      }
    }
  }

  Future<void> _pickImageFromCamera() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (image != null) {
        // Dosyayı byte array'e çevir
        final Uint8List imageBytes = await image.readAsBytes();

        // Direkt yükle
        _uploadCroppedPhoto(imageBytes);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fotoğraf çekme hatası: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }



  void _removePhoto() async {
    try {
      // Eski fotoğrafı sil (varsa)
      if (_avatarController.text.isNotEmpty) {
        await _storageService.deleteProfilePhoto(_avatarController.text);
      }

      setState(() {
        _avatarController.text = '';
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profil fotoğrafı kaldırıldı'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fotoğraf kaldırma hatası: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
