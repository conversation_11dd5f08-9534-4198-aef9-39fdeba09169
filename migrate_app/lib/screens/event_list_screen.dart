import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/event_model.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';
import '../services/event_service.dart';
import '../widgets/event_card.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/responsive_container.dart';
import '../widgets/empty_events_view.dart';
import 'create_event_screen.dart';
import 'login_screen.dart';
import 'profile_screen.dart';

class EventListScreen extends StatefulWidget {
  const EventListScreen({Key? key}) : super(key: key);

  @override
  State<EventListScreen> createState() => _EventListScreenState();
}

class _EventListScreenState extends State<EventListScreen> {
  final AuthService _authService = AuthService();
  final EventService _eventService = EventService();
  late Future<List<Event>> _eventsFuture;
  AppUser? _currentUser;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserAndEvents();
  }

  Future<void> _loadUserAndEvents() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get current user data
      _currentUser = await _authService.getCurrentUserData();

      // Load events for user's city
      if (_currentUser != null) {
        _eventsFuture = _eventService.getEventsByCity(_currentUser!.city);
      } else {
        _eventsFuture = _eventService.getAllEvents();
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Hata: ${e.toString()}')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshEvents() async {
    setState(() {
      if (_currentUser != null) {
        _eventsFuture = _eventService.getEventsByCity(_currentUser!.city);
      } else {
        _eventsFuture = _eventService.getAllEvents();
      }
    });
  }

  Future<void> _signOut() async {
    try {
      await _authService.signOut();
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Çıkış yapılırken hata: ${e.toString()}')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Migrate App - Etkinlikler',
        currentUser: _currentUser,
        showBackButton: false,
        onAdminPanelPressed: _currentUser?.isAdmin == true ? () {
          // Navigate to admin panel or show admin options
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Admin paneli yakında eklenecek')),
          );
        } : null,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Yenile',
            onPressed: _refreshEvents,
          ),
          IconButton(
            icon: const Icon(Icons.person),
            tooltip: 'Profil',
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const ProfileScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            tooltip: 'Çıkış Yap',
            onPressed: _signOut,
          ),
        ],
      ),
      body: ResponsiveContainer(
        maxWidth: 800, // Etkinlik listesi için daha geniş
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _buildEventList(),
      ),
      floatingActionButton: _currentUser?.isAdmin == true
          ? FloatingActionButton(
              onPressed: () async {
                final result = await Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const CreateEventScreen(),
                  ),
                );
                if (result == true) {
                  _refreshEvents();
                }
              },
              tooltip: 'Etkinlik Ekle',
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  Widget _buildEventList() {
    return FutureBuilder<List<Event>>(
      future: _eventsFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text('Hata: ${snapshot.error}'),
          );
        }

        final events = snapshot.data ?? [];

        if (events.isEmpty) {
          return EmptyEventsView(
            currentUser: _currentUser,
            onRefresh: _refreshEvents,
          );
        }

        return RefreshIndicator(
          onRefresh: _refreshEvents,
          child: ListView.builder(
            padding: const EdgeInsets.all(8.0),
            itemCount: events.length,
            itemBuilder: (context, index) {
              final event = events[index];
              return EventCard(
                event: event,
                currentUserId: _authService.currentUser?.id ?? '',
                onParticipationToggled: _refreshEvents,
                isAdmin: _currentUser?.isAdmin ?? false,
              );
            },
          ),
        );
      },
    );
  }
}
