import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../models/post_model.dart';
import '../models/user_profile_model.dart';
import '../services/auth_service.dart';
import '../services/post_service.dart';
import '../services/user_profile_service.dart';
import '../widgets/responsive_container.dart';
import 'create_post_screen.dart';
import 'profile_edit_screen.dart';
import 'countries_list_screen.dart';
import 'event_list_screen.dart';

class MainFeedScreen extends StatefulWidget {
  const MainFeedScreen({Key? key}) : super(key: key);

  @override
  State<MainFeedScreen> createState() => _MainFeedScreenState();
}

class _MainFeedScreenState extends State<MainFeedScreen> {
  final AuthService _authService = AuthService();
  final PostService _postService = PostService();
  final UserProfileService _profileService = UserProfileService();
  AppUser? _currentUser;
  UserProfile? _currentUserProfile;
  List<Post> _posts = [];
  bool _isLoading = true;
  bool _isLoadingPosts = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
    _loadUserProfile();
    _loadPosts();
  }

  Future<void> _loadUserData() async {
    try {
      final user = await _authService.getCurrentUserData();
      setState(() {
        _currentUser = user;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadUserProfile() async {
    try {
      final profile = await _profileService.getCurrentUserProfile();
      setState(() {
        _currentUserProfile = profile;
      });
    } catch (e) {
      print('Profil yükleme hatası: $e');
    }
  }

  Future<void> _loadPosts() async {
    setState(() {
      _isLoadingPosts = true;
    });

    try {
      final posts = await _postService.getFeedPosts();
      setState(() {
        _posts = posts;
        _isLoadingPosts = false;
      });
    } catch (e) {
      print('Post yükleme hatası: $e');
      setState(() {
        _isLoadingPosts = false;
      });
    }
  }

  Future<void> _refreshFeed() async {
    await _loadPosts();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: _buildAppBar(),
      drawer: _buildMobileDrawer(context),
      body: _buildResponsiveLayout(context),
      bottomNavigationBar: _buildBottomNavigation(context),
    );
  }

  Widget _buildResponsiveLayout(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= 1200) {
          // Desktop: 3 sütun
          return _buildDesktopLayout();
        } else if (constraints.maxWidth >= 768) {
          // Tablet: 2 sütun
          return _buildTabletLayout();
        } else {
          // Mobile: 1 sütun
          return _buildMobileLayout();
        }
      },
    );
  }

  Widget _buildDesktopLayout() {
    return ResponsiveContainer(
      maxWidth: 1200,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Sol Sidebar
          SizedBox(
            width: 280,
            child: _buildLeftSidebar(),
          ),

          // Ana Feed
          Expanded(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 12),
              child: _buildMainFeed(),
            ),
          ),

          // Sağ Sidebar
          SizedBox(
            width: 280,
            child: _buildRightSidebar(),
          ),
        ],
      ),
    );
  }

  Widget _buildTabletLayout() {
    return ResponsiveContainer(
      maxWidth: 1000,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Ana Feed
          Expanded(
            flex: 2,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 12),
              child: _buildMainFeed(),
            ),
          ),

          // Sağ Sidebar (daraltılmış)
          SizedBox(
            width: 260,
            child: _buildRightSidebar(),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileLayout() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      child: _buildMainFeed(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 1,
      // leading: null olarak bırakırsak Flutter otomatik drawer butonu ekler
      title: _buildAppBarTitle(),
      actions: _buildAppBarActions(),
    );
  }

  Widget _buildAppBarTitle() {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 768) {
      // Mobile: Sadece logo
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.blue,
          borderRadius: BorderRadius.circular(6),
        ),
        child: const Text(
          'MC',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
      );
    } else {
      // Tablet/Desktop: Logo + Arama
      return Row(
        children: [
          // Logo
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.blue,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              screenWidth < 1200 ? 'MC' : 'MigrateConnect',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),

          const SizedBox(width: 20),

          // Arama çubuğu
          Expanded(
            child: Container(
              height: 40,
              constraints: const BoxConstraints(maxWidth: 400),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(20),
              ),
              child: const TextField(
                decoration: InputDecoration(
                  hintText: 'Ara...',
                  prefixIcon: Icon(Icons.search, color: Colors.grey),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ),
        ],
      );
    }
  }

  List<Widget> _buildAppBarActions() {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 768) {
      // Mobile: Sadece arama ve profil
      return [
        IconButton(
          icon: const Icon(Icons.search, color: Colors.grey),
          onPressed: () {
            // Arama modalı aç
          },
        ),
        Padding(
          padding: const EdgeInsets.only(right: 8),
          child: GestureDetector(
            onTap: () {
              Navigator.pushNamed(context, '/profile');
            },
            child: CircleAvatar(
              radius: 16,
              backgroundColor: Colors.blue,
              backgroundImage: _currentUserProfile?.avatar?.isNotEmpty == true
                  ? NetworkImage(_currentUserProfile!.avatar!)
                  : null,
              child: _currentUserProfile?.avatar?.isNotEmpty != true
                  ? Text(
                      _currentUser?.email?.substring(0, 1).toUpperCase() ?? 'U',
                      style: const TextStyle(color: Colors.white, fontSize: 14),
                    )
                  : null,
            ),
          ),
        ),
      ];
    } else {
      // Tablet/Desktop: Tüm ikonlar
      return [
        IconButton(
          icon: Stack(
            children: [
              const Icon(Icons.notifications_outlined, color: Colors.grey),
              Positioned(
                right: 0,
                top: 0,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ],
          ),
          onPressed: () {},
        ),

        IconButton(
          icon: const Icon(Icons.message_outlined, color: Colors.grey),
          onPressed: () {},
        ),

        Padding(
          padding: const EdgeInsets.only(right: 16),
          child: GestureDetector(
            onTap: () {
              Navigator.pushNamed(context, '/profile');
            },
            child: CircleAvatar(
              radius: 16,
              backgroundColor: Colors.blue,
              backgroundImage: _currentUserProfile?.avatar?.isNotEmpty == true
                  ? NetworkImage(_currentUserProfile!.avatar!)
                  : null,
              child: _currentUserProfile?.avatar?.isNotEmpty != true
                  ? Text(
                      _currentUser?.email?.substring(0, 1).toUpperCase() ?? 'U',
                      style: const TextStyle(color: Colors.white, fontSize: 14),
                    )
                  : null,
            ),
          ),
        ),
      ];
    }
  }

  Widget _buildLeftSidebar() {
    return Container(
      padding: const EdgeInsets.all(12),
      child: SingleChildScrollView(
        child: Column(
          children: [
            // Profil Özeti
            _buildProfileSummary(),

            const SizedBox(height: 16),

            // Menü
            _buildNavigationMenu(),

            const SizedBox(height: 16),

            // Hızlı Erişim
            _buildQuickAccess(),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileSummary() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Profil fotoğrafı ve düzenleme butonu
          Stack(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: Colors.blue,
                backgroundImage: _currentUserProfile?.avatar?.isNotEmpty == true
                    ? NetworkImage(_currentUserProfile!.avatar!)
                    : null,
                child: _currentUserProfile?.avatar?.isNotEmpty != true
                    ? Text(
                        _currentUserProfile?.firstName.isNotEmpty == true
                            ? _currentUserProfile!.firstName.substring(0, 1).toUpperCase()
                            : _currentUser?.email?.substring(0, 1).toUpperCase() ?? 'U',
                        style: const TextStyle(color: Colors.white, fontSize: 24),
                      )
                    : null,
              ),
              Positioned(
                right: 0,
                bottom: 0,
                child: GestureDetector(
                  onTap: _openProfileEdit,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: const BoxDecoration(
                      color: Colors.blue,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.edit,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            _currentUserProfile?.displayName ?? _currentUser?.email?.split('@')[0] ?? 'Kullanıcı',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 17,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            _currentUserProfile?.migrationRoute ?? 'Göç rotası belirtilmemiş',
            style: const TextStyle(
              color: Colors.grey,
              fontSize: 13,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _currentUserProfile?.profession ?? 'Meslek belirtilmemiş',
            style: const TextStyle(
              color: Colors.blue,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),

          // Profil tamamlanma çubuğu
          if (_currentUserProfile != null) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: LinearProgressIndicator(
                    value: _currentUserProfile!.profileCompletionPercentage / 100,
                    backgroundColor: Colors.grey[200],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _currentUserProfile!.profileCompletionPercentage >= 80
                          ? Colors.green
                          : _currentUserProfile!.profileCompletionPercentage >= 50
                              ? Colors.orange
                              : Colors.red,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '%${_currentUserProfile!.profileCompletionPercentage}',
                  style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              'Profil tamamlanma oranı',
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey[600],
              ),
            ),
          ],

          // Profil düzenleme butonu
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: _openProfileEdit,
              style: OutlinedButton.styleFrom(
                side: const BorderSide(color: Colors.blue),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Profili Düzenle',
                style: TextStyle(
                  color: Colors.blue,
                  fontSize: 12,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationMenu() {
    final menuItems = [
      {'icon': Icons.home, 'title': 'Ana Sayfa', 'active': true},
      {'icon': Icons.people, 'title': 'Ağım', 'active': false},
      {'icon': Icons.message, 'title': 'Mesajlar', 'active': false},
      {'icon': Icons.event, 'title': 'Etkinlikler', 'active': false},
      {'icon': Icons.work, 'title': 'İş İlanları', 'active': false},
      {'icon': Icons.group, 'title': 'Gruplar', 'active': false},
      {'icon': Icons.public, 'title': 'Ülke Rehberi', 'active': false},
    ];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: menuItems.map((item) {
          final isActive = item['active'] as bool;
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            child: ListTile(
              leading: Icon(
                item['icon'] as IconData,
                color: isActive ? Colors.blue : Colors.grey,
              ),
              title: Text(
                item['title'] as String,
                style: TextStyle(
                  color: isActive ? Colors.blue : Colors.grey[700],
                  fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                  fontSize: 15,
                ),
              ),
              dense: true,
              contentPadding: const EdgeInsets.symmetric(horizontal: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              tileColor: isActive ? Colors.blue.withOpacity(0.1) : null,
              onTap: () {},
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildQuickAccess() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Hızlı Erişim',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 12),
          _buildQuickAccessItem('🏠', 'Konut Arama', 'Yeni'),
          _buildQuickAccessItem('💼', 'İş Fırsatları', '12'),
          _buildQuickAccessItem('🎓', 'Eğitim Rehberi', ''),
          _buildQuickAccessItem('🏥', 'Sağlık Sistemi', ''),
        ],
      ),
    );
  }

  Widget _buildQuickAccessItem(String emoji, String title, String badge) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 16)),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              title,
              style: const TextStyle(fontSize: 14),
            ),
          ),
          if (badge.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                badge,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMainFeed() {
    return RefreshIndicator(
      onRefresh: _refreshFeed,
      child: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 16),

            // Post Oluştur
            _buildCreatePost(),

            const SizedBox(height: 16),

            // Feed Posts
            if (_isLoadingPosts)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: CircularProgressIndicator(),
                ),
              )
            else if (_posts.isEmpty)
              _buildEmptyFeed()
            else
              ..._posts.map((post) => _buildFeedPost(post)).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildCreatePost() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: Colors.blue,
                backgroundImage: _currentUserProfile?.avatar?.isNotEmpty == true
                    ? NetworkImage(_currentUserProfile!.avatar!)
                    : null,
                child: _currentUserProfile?.avatar?.isNotEmpty != true
                    ? Text(
                        _currentUser?.email?.substring(0, 1).toUpperCase() ?? 'U',
                        style: const TextStyle(color: Colors.white),
                      )
                    : null,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: GestureDetector(
                  onTap: () => _openCreatePost(context),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    child: const Text(
                      'Deneyimini paylaş, soru sor veya yardım et...',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildPostTypeButton('📝', 'Deneyim', PostType.experience),
                _buildPostTypeButton('❓', 'Soru', PostType.question),
                _buildPostTypeButton('📅', 'Etkinlik', PostType.event),
                _buildPostTypeButton('💼', 'İş İlanı', PostType.job),
                _buildPostTypeButton('🎉', 'Başarı', PostType.success),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostTypeButton(String emoji, String label, PostType type) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      width: 85,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _openCreatePost(context, type),
          borderRadius: BorderRadius.circular(10),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 6),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: Colors.grey[200]!,
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.grey[100]!,
                      width: 1,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      emoji,
                      style: const TextStyle(fontSize: 18),
                    ),
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[700],
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }



  Widget _buildEmptyFeed() {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.forum_outlined,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Text(
            'Henüz hiç paylaşım yok',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'İlk paylaşımı sen yap ve topluluğu büyütmeye yardım et!',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            onPressed: () => _openCreatePost(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('İlk Paylaşımı Yap'),
          ),
        ],
      ),
    );
  }

  Widget _buildFeedPost(Post post) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Post header
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: _getPostTypeColor(post.type),
                child: Text(
                  post.userName.isNotEmpty
                      ? post.userName.substring(0, 1).toUpperCase()
                      : post.userEmail.substring(0, 1).toUpperCase(),
                  style: const TextStyle(color: Colors.white),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          post.type.emoji,
                          style: const TextStyle(fontSize: 16),
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            post.userName.isNotEmpty ? post.userName : post.userEmail.split('@')[0],
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 15,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    Text(
                      '${post.userProfession} • ${post.userLocation} • ${_getTimeAgo(post.createdAt)}',
                      style: const TextStyle(color: Colors.grey, fontSize: 13),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              const Icon(Icons.more_horiz, color: Colors.grey),
            ],
          ),

          const SizedBox(height: 12),

          // Post title
          Text(
            post.title,
            style: const TextStyle(
              fontSize: 17,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 8),

          // Post content
          Text(
            post.content,
            style: const TextStyle(fontSize: 15),
          ),

          // Tags
          if (post.tags.isNotEmpty) ...[
            const SizedBox(height: 12),
            Wrap(
              spacing: 6,
              runSpacing: 6,
              children: post.tags.map((tag) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '#$tag',
                    style: const TextStyle(
                      color: Colors.blue,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],

          const SizedBox(height: 12),

          // Post actions
          Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    Icon(
                      post.isLiked ? Icons.thumb_up : Icons.thumb_up_outlined,
                      color: post.isLiked ? Colors.blue : Colors.grey,
                      size: 20,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${post.likesCount}',
                      style: const TextStyle(color: Colors.grey, fontSize: 13),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Row(
                  children: [
                    const Icon(Icons.comment_outlined, color: Colors.grey, size: 20),
                    const SizedBox(width: 4),
                    Text(
                      '${post.commentsCount}',
                      style: const TextStyle(color: Colors.grey, fontSize: 13),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Row(
                  children: [
                    const Icon(Icons.share_outlined, color: Colors.grey, size: 20),
                    const SizedBox(width: 4),
                    const Text(
                      'Paylaş',
                      style: TextStyle(color: Colors.grey, fontSize: 13),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRightSidebar() {
    return Container(
      padding: const EdgeInsets.all(12),
      child: SingleChildScrollView(
        child: Column(
          children: [
            // Önerilen Kişiler
            _buildSuggestedPeople(),

            const SizedBox(height: 16),

            // Yaklaşan Etkinlikler
            _buildUpcomingEvents(),

            const SizedBox(height: 16),

            // Popüler Konular
            _buildTrendingTopics(),
          ],
        ),
      ),
    );
  }

  Widget _buildSuggestedPeople() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Tanışabileceğin Kişiler',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 12),
          _buildPersonSuggestion('Elif Kaya', 'UX Designer • Hollanda', 'E'),
          _buildPersonSuggestion('Mehmet Öz', 'Doktor • Almanya', 'M'),
          _buildPersonSuggestion('Zeynep Ak', 'Öğretmen • Kanada', 'Z'),
        ],
      ),
    );
  }

  Widget _buildPersonSuggestion(String name, String info, String initial) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          CircleAvatar(
            radius: 14,
            backgroundColor: Colors.purple,
            child: Text(
              initial,
              style: const TextStyle(color: Colors.white, fontSize: 11),
            ),
          ),
          const SizedBox(width: 6),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 13),
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  info,
                  style: const TextStyle(color: Colors.grey, fontSize: 12),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          const SizedBox(width: 4),
          ElevatedButton(
            onPressed: () {},
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              minimumSize: Size.zero,
            ),
            child: const Text(
              'Bağlan',
              style: TextStyle(fontSize: 11, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpcomingEvents() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Yaklaşan Etkinlikler',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 12),
          _buildEventItem('Amsterdam Türk Buluşması', '15 Oca', '🇳🇱'),
          _buildEventItem('Berlin İş Ağı Toplantısı', '18 Oca', '🇩🇪'),
          _buildEventItem('Online Vize Semineri', '22 Oca', '💻'),
        ],
      ),
    );
  }

  Widget _buildEventItem(String title, String date, String emoji) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 14)),
          const SizedBox(width: 6),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  date,
                  style: const TextStyle(color: Colors.grey, fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrendingTopics() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Popüler Konular',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 6,
            runSpacing: 6,
            children: [
              _buildTopicChip('#VisaProblemi'),
              _buildTopicChip('#BerlinIş'),
              _buildTopicChip('#HollandaKonut'),
              _buildTopicChip('#AlmancaÖğren'),
              _buildTopicChip('#NetworkingTips'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTopicChip(String topic) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        topic,
        style: const TextStyle(
          color: Colors.blue,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  void _openCreatePost(BuildContext context, [PostType? type]) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreatePostScreen(initialType: type),
      ),
    ).then((result) {
      if (result != null && result is Post) {
        // Yeni post eklendi, feed'i yenile
        _refreshFeed();
      }
    });
  }

  Color _getPostTypeColor(PostType type) {
    switch (type) {
      case PostType.experience:
        return Colors.green;
      case PostType.question:
        return Colors.orange;
      case PostType.event:
        return Colors.purple;
      case PostType.job:
        return Colors.blue;
      case PostType.success:
        return Colors.red;
    }
  }

  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} gün önce';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} saat önce';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} dakika önce';
    } else {
      return 'Az önce';
    }
  }

  void _openProfileEdit() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProfileEditScreen(existingProfile: _currentUserProfile),
      ),
    ).then((result) {
      if (result != null && result is UserProfile) {
        // Profil güncellendi, yeniden yükle
        _loadUserProfile();
      }
    });
  }

  Widget _buildMobileDrawer(BuildContext context) {
    return Drawer(
      child: SafeArea(
        child: Column(
          children: [
            // Profil Özeti
            Container(
              padding: const EdgeInsets.all(16),
              child: _buildProfileSummary(),
            ),

            const Divider(),

            // Menü
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  _buildDrawerItem(Icons.home, 'Ana Sayfa', true),
                  _buildDrawerItem(Icons.people, 'Ağım', false),
                  _buildDrawerItem(Icons.message, 'Mesajlar', false),
                  _buildDrawerItem(Icons.event, 'Etkinlikler', false),
                  _buildDrawerItem(Icons.work, 'İş İlanları', false),
                  _buildDrawerItem(Icons.group, 'Gruplar', false),
                  _buildDrawerItem(Icons.public, 'Ülke Rehberi', false),
                  const Divider(),
                  _buildDrawerItem(Icons.settings, 'Ayarlar', false),
                  _buildDrawerItem(Icons.help, 'Yardım', false),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerItem(IconData icon, String title, bool isActive) {
    return ListTile(
      leading: Icon(
        icon,
        color: isActive ? Colors.blue : Colors.grey[600],
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isActive ? Colors.blue : Colors.grey[800],
          fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
          fontSize: 15,
        ),
      ),
      selected: isActive,
      selectedTileColor: Colors.blue.withOpacity(0.1),
      onTap: () {
        print('ListTile onTap çalıştı: $title'); // Debug
        Navigator.pop(context);
        _handleDrawerItemTap(title);
      },
    );
  }

  void _handleDrawerItemTap(String title) {
    print('Drawer item tapped: $title'); // Debug için

    switch (title) {
      case 'Ülke Rehberi':
        print('Navigating to Countries List'); // Debug için
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const CountriesListScreen()),
        );
        break;
      case 'Etkinlikler':
        print('Navigating to Events List'); // Debug için
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const EventListScreen()),
        );
        break;
      case 'Ayarlar':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Ayarlar sayfası yakında eklenecek')),
        );
        break;
      case 'Yardım':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Yardım sayfası yakında eklenecek')),
        );
        break;
      default:
        print('Unknown drawer item: $title'); // Debug için
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('$title sayfası yakında eklenecek')),
        );
        break;
    }
  }

  Widget? _buildBottomNavigation(BuildContext context) {
    // Sadece mobile'da bottom navigation göster
    return MediaQuery.of(context).size.width < 768
        ? BottomNavigationBar(
            type: BottomNavigationBarType.fixed,
            selectedItemColor: Colors.blue,
            unselectedItemColor: Colors.grey,
            currentIndex: 0,
            items: const [
              BottomNavigationBarItem(
                icon: Icon(Icons.home),
                label: 'Ana Sayfa',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.people),
                label: 'Ağım',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.add_box_outlined),
                label: 'Paylaş',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.notifications),
                label: 'Bildirimler',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.message),
                label: 'Mesajlar',
              ),
            ],
            onTap: (index) {
              if (index == 2) {
                _openCreatePost(context);
              }
            },
          )
        : null;
  }
}
