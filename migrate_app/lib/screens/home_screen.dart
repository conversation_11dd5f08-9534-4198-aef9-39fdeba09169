import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';
import '../utils/admin_helper.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/responsive_container.dart';
import '../widgets/home_cards/login_register_card.dart';
import '../widgets/home_cards/about_app_card.dart';
import '../widgets/home_cards/events_login_card.dart';
import '../widgets/home_cards/create_event_card.dart';
import '../widgets/home_cards/discover_events_card.dart';
import '../widgets/home_cards/my_events_card.dart';
import '../widgets/home_cards/communities_card.dart';
import '../widgets/home_cards/profile_card.dart';
import '../widgets/home_cards/country_guide_card.dart';
import 'event_list_screen.dart';
import 'profile_screen.dart';
import 'admin/admin_panel_screen.dart';
import 'main_feed_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final AuthService _authService = AuthService();
  AppUser? _currentUser;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    // Hata ayıklama mesajları
    debugPrint('HomeScreen: initState çağrıldı');
    debugPrint('HomeScreen: Kullanıcı giriş yapmış mı: ${_authService.isLoggedIn}');

    // Kullanıcı verilerini yükle
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
    });

    debugPrint('HomeScreen: Kullanıcı bilgileri yükleniyor...');
    debugPrint('HomeScreen: Kullanıcı giriş yapmış mı: ${_authService.isLoggedIn}');

    try {
      if (_authService.isLoggedIn) {
        // Kullanıcı giriş yapmışsa, kullanıcı verilerini yükle
        _currentUser = await _authService.getCurrentUserData();
        debugPrint('HomeScreen: Kullanıcı bilgileri yüklendi: ${_currentUser?.email}');
        debugPrint('HomeScreen: Kullanıcı admin mi: ${_currentUser?.isAdmin}');
      } else {
        // Kullanıcı giriş yapmamışsa
        debugPrint('HomeScreen: Kullanıcı giriş yapmamış');
      }
    } catch (e) {
      // Hata durumunda
      debugPrint('HomeScreen: Kullanıcı bilgileri yüklenirken hata: $e');
    } finally {
      // Widget hala monte edilmişse, durumu güncelle
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
      debugPrint('HomeScreen: Yükleme tamamlandı, isLoading: $_isLoading');
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('HomeScreen: build çağrıldı, isLoggedIn: ${_authService.isLoggedIn}, isLoading: $_isLoading');

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Migrate App',
        currentUser: _currentUser,
        showBackButton: false,
        onAdminPanelPressed: _currentUser?.isAdmin == true ? () {
          // Admin paneline yönlendir
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const AdminPanelScreen(),
            ),
          );
        } : null,
        actions: [
          // Debug butonu - test verisi oluştur
          if (_authService.isLoggedIn && _currentUser?.isAdmin == true)
            IconButton(
              icon: const Icon(Icons.data_object),
              tooltip: 'Test Verisi Oluştur',
              onPressed: () async {
                try {
                  await AdminHelper.createTestData();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Test verisi oluşturuldu!'),
                      backgroundColor: Colors.green,
                    ),
                  );
                } catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Hata: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
            ),
          // Debug butonu - kendini admin yap
          if (_authService.isLoggedIn && _currentUser?.isAdmin != true)
            IconButton(
              icon: const Icon(Icons.admin_panel_settings),
              tooltip: 'Kendimi Admin Yap',
              onPressed: () async {
                try {
                  final success = await AdminHelper.makeCurrentUserAdmin();
                  if (success) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Başarıyla admin yetkisi aldınız! Sayfayı yenileyin.'),
                        backgroundColor: Colors.green,
                      ),
                    );
                    // Kullanıcı verilerini yenile
                    await _loadUserData();
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Admin yetkisi verilemedi.'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                } catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Hata: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
            ),
          if (_authService.isLoggedIn)
            IconButton(
              icon: const Icon(Icons.person),
              tooltip: 'Profil',
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const ProfileScreen(),
                  ),
                );
              },
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildHomeContent(),
    );
  }

  Widget _buildHomeContent() {
    // Giriş yapmış kullanıcılar için ana feed'i göster
    if (_authService.isLoggedIn) {
      return const MainFeedScreen();
    }

    // Giriş yapmamış kullanıcılar için kartları göster
    return ResponsiveContainer(
      maxWidth: 1200,
      child: RefreshIndicator(
        onRefresh: _loadUserData,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Karşılama mesajı
              _buildWelcomeSection(),

              const SizedBox(height: 24),

              // Kartlar
              _buildCardsSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    final bool isLoggedIn = _authService.isLoggedIn;
    final String userName = _currentUser?.email.split('@').first ?? '';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isLoggedIn
              ? 'Merhaba, $userName 👋'
              : 'Migrate App\'e Hoş Geldiniz 👋',
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          isLoggedIn
              ? 'Bugün neler yapmak istersiniz?'
              : 'Yurt dışındaki Türklerle bağlantı kurun',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildCardsSection() {
    debugPrint('HomeScreen: _buildCardsSection çağrıldı, isLoggedIn: ${_authService.isLoggedIn}');

    // Kullanıcı giriş yapmamışsa
    if (!_authService.isLoggedIn) {
      debugPrint('HomeScreen: Kullanıcı giriş yapmamış, giriş kartları gösteriliyor');
      return LayoutBuilder(
        builder: (context, constraints) {
          // Ekran genişliğine göre grid veya liste görünümü
          if (constraints.maxWidth > 600) {
            return GridView.count(
              crossAxisCount: constraints.maxWidth > 900 ? 3 : 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: const [
                LoginRegisterCard(),
                CountryGuideCard(),
                AboutAppCard(),
                EventsLoginCard(),
              ],
            );
          } else {
            return const Column(
              children: [
                LoginRegisterCard(),
                SizedBox(height: 16),
                CountryGuideCard(),
                SizedBox(height: 16),
                AboutAppCard(),
                SizedBox(height: 16),
                EventsLoginCard(),
              ],
            );
          }
        },
      );
    }

    // Kullanıcı giriş yapmışsa
    return LayoutBuilder(
      builder: (context, constraints) {
        final List<Widget> cards = [
          const CountryGuideCard(),
          if (_currentUser?.isAdmin == true)
            CreateEventCard(
              onEventCreated: _loadUserData,
            ),
          if (_currentUser != null)
            DiscoverEventsCard(
              user: _currentUser!,
            ),
          if (_currentUser != null)
            MyEventsCard(
              user: _currentUser!,
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const EventListScreen(),
                  ),
                );
              },
            ),
          const CommunitiesCard(),
          if (_currentUser != null)
            ProfileCard(
              user: _currentUser!,
            ),
        ];

        // Ekran genişliğine göre grid veya liste görünümü
        if (constraints.maxWidth > 600) {
          return GridView.count(
            crossAxisCount: constraints.maxWidth > 900 ? 3 : 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            childAspectRatio: 1.1,
            children: cards,
          );
        } else {
          return Column(
            children: [
              for (int i = 0; i < cards.length; i++) ...[
                cards[i],
                if (i < cards.length - 1) const SizedBox(height: 16),
              ],
            ],
          );
        }
      },
    );
  }
}
