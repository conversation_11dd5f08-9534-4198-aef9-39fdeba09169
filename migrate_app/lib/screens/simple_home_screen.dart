import 'package:flutter/material.dart';
import '../widgets/responsive_container.dart';
import '../widgets/home_cards/about_app_card.dart';
import '../widgets/home_cards/events_login_card.dart';

class SimpleHomeScreen extends StatelessWidget {
  const SimpleHomeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Migrate App'),
        actions: [
          TextButton.icon(
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('<PERSON><PERSON>ş özelliği yakında aktif olacak!')),
              );
            },
            icon: const Icon(Icons.login, color: Colors.white),
            label: const Text('<PERSON><PERSON><PERSON> Yap', style: TextStyle(color: Colors.white)),
          ),
          const SizedBox(width: 8),
          TextButton.icon(
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('<PERSON><PERSON>t özelliği yakında aktif olacak!')),
              );
            },
            icon: const Icon(Icons.person_add, color: Colors.white),
            label: const Text('Kayıt Ol', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
      body: ResponsiveContainer(
        maxWidth: 1200,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Karşılama mesajı
              _buildWelcomeSection(),

              const SizedBox(height: 24),

              // Kartlar
              _buildCardsSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Migrate App\'e Hoş Geldiniz 👋',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Yurt dışındaki Türklerle bağlantı kurun',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildCardsSection() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Ekran genişliğine göre grid veya liste görünümü
        if (constraints.maxWidth > 600) {
          return GridView.count(
            crossAxisCount: constraints.maxWidth > 900 ? 3 : 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              _buildLoginCard(context),
              const AboutAppCard(),
              const EventsLoginCard(),
            ],
          );
        } else {
          return Column(
            children: [
              _buildLoginCard(context),
              const SizedBox(height: 16),
              const AboutAppCard(),
              const SizedBox(height: 16),
              const EventsLoginCard(),
            ],
          );
        }
      },
    );
  }

  Widget _buildLoginCard(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Giriş özelliği yakında aktif olacak!')),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.login_rounded,
                      color: Colors.blue,
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Giriş Yap',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          'Hesabınıza giriş yapın',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              const Text(
                'Migrate App\'in tüm özelliklerinden yararlanmak için giriş yapın.',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 16),
              Align(
                alignment: Alignment.centerRight,
                child: ElevatedButton.icon(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Giriş özelliği yakında aktif olacak!')),
                    );
                  },
                  icon: const Icon(Icons.login),
                  label: const Text('Giriş Yap'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
