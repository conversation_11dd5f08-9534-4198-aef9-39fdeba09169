import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class SimpleAdminScreen extends StatelessWidget {
  const SimpleAdminScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🔧 Admin Panel (Test)'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Başarı mesajı
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.green[600]!, Colors.green[400]!],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: Colors.white,
                        size: 32,
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          '🎉 Başarılı!',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Ülke Rehberi özelliği başarıyla çalışıyor!',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Özellikler listesi
            const Text(
              '✅ Tamamlanan Özellikler',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            _buildFeatureCard(
              '🌍 Ülke Rehberi Kartı',
              'Ana sayfada ülke rehberi kartı görünüyor',
              Colors.green,
              true,
            ),
            
            const SizedBox(height: 12),
            
            _buildFeatureCard(
              '🔧 Admin Panel',
              'Admin panel sayfası açılıyor (test modu)',
              Colors.green,
              true,
            ),
            
            const SizedBox(height: 12),
            
            _buildFeatureCard(
              '📱 Responsive Tasarım',
              'Mobil ve web uyumlu tasarım',
              Colors.green,
              true,
            ),
            
            const SizedBox(height: 12),
            
            _buildFeatureCard(
              '🗃️ Supabase Entegrasyonu',
              'Veritabanı bağlantısı hazır',
              Colors.green,
              true,
            ),
            
            const SizedBox(height: 24),
            
            // Sonraki adımlar
            const Text(
              '🚀 Sonraki Adımlar',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            _buildFeatureCard(
              '📝 Ülke Ekleme Formu',
              'Admin panelinde ülke ekleme özelliği',
              Colors.orange,
              false,
            ),
            
            const SizedBox(height: 12),
            
            _buildFeatureCard(
              '📋 Ülke Detay Sayfası',
              'Ülke bilgilerini görüntüleme sayfası',
              Colors.orange,
              false,
            ),
            
            const SizedBox(height: 12),
            
            _buildFeatureCard(
              '🔐 Authentication Düzeltme',
              'Supabase auth callback URL düzeltme',
              Colors.orange,
              false,
            ),
            
            const SizedBox(height: 24),
            
            // Test butonları
            const Text(
              '🧪 Test Butonları',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Fluttertoast.showToast(
                        msg: "Ülke listesi özelliği test edildi! ✅",
                        toastLength: Toast.LENGTH_SHORT,
                        gravity: ToastGravity.BOTTOM,
                      );
                    },
                    icon: const Icon(Icons.public),
                    label: const Text('Ülke Listesi Test'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    icon: const Icon(Icons.home),
                    label: const Text('Ana Sayfaya Dön'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Bilgi kutusu
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info,
                        color: Colors.blue[600],
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Bilgi',
                        style: TextStyle(
                          color: Colors.blue[600],
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Ülke Rehberi özelliği başarıyla eklendi! Ana sayfada ülke rehberi kartını görebilir ve admin paneline erişebilirsiniz. Authentication sorunu çözüldükten sonra tam özellikler aktif olacak.',
                    style: TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureCard(String title, String description, Color color, bool isCompleted) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                isCompleted ? Icons.check_circle : Icons.schedule,
                color: color,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            if (isCompleted)
              Icon(
                Icons.check,
                color: color,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }
}
