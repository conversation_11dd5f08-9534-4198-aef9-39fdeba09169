import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'dart:convert';
import '../../models/user_model.dart';
import '../../models/country_page_model.dart';
import '../../services/auth_service.dart';
import '../../services/country_page_service.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/responsive_container.dart';
import '../../widgets/icon_picker_widget.dart';
import '../../widgets/rich_text_editor.dart';
import '../../widgets/image_picker_widget.dart';

class AdminCountryEditScreen extends StatefulWidget {
  final CountryPage? countryPage; // null ise yeni ülke ekleme

  const AdminCountryEditScreen({
    Key? key,
    this.countryPage,
  }) : super(key: key);

  @override
  State<AdminCountryEditScreen> createState() => _AdminCountryEditScreenState();
}

class _AdminCountryEditScreenState extends State<AdminCountryEditScreen> with TickerProviderStateMixin {
  final AuthService _authService = AuthService();
  final CountryPageService _countryPageService = CountryPageService();

  AppUser? _currentUser;
  bool _isLoading = true;
  bool _isSaving = false;

  // Tab Controller
  late TabController _tabController;

  // Form Controllers
  final _formKey = GlobalKey<FormState>();
  final _countryController = TextEditingController();
  final _jsonController = TextEditingController();

  // Data
  List<CountrySection> _sections = [];

  // Categories
  final List<String> _categories = ['Yaşam', 'Göç', 'İş', 'Topluluk', 'Genel Bilgi'];

  // Current editing section
  int? _editingSectionIndex;

  bool get _isEditing => widget.countryPage != null;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _countryController.dispose();
    _jsonController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Kullanıcı bilgilerini yükle
      _currentUser = await _authService.getCurrentUserData();
      
      // Admin kontrolü
      if (_currentUser?.isAdmin != true) {
        if (mounted) {
          Navigator.of(context).pop();
          Fluttertoast.showToast(
            msg: "Bu sayfaya erişim yetkiniz yok",
            toastLength: Toast.LENGTH_LONG,
            gravity: ToastGravity.BOTTOM,
          );
        }
        return;
      }

      // Düzenleme modundaysa mevcut verileri yükle
      if (_isEditing) {
        _countryController.text = widget.countryPage!.country;
        _sections = List.from(widget.countryPage!.sections);
        _updateJsonFromSections();
      } else {
        // Yeni ülke için boş bir bölüm ekle
        _addNewSection();
      }
    } catch (e) {
      if (mounted) {
        Fluttertoast.showToast(
          msg: "Veriler yüklenirken hata oluştu: $e",
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _addNewSection() {
    setState(() {
      _sections.add(
        CountrySection(
          title: '',
          category: 'Yaşam',
          icon: '🏡',
          content: '',
          links: [],
          tags: [],
        ),
      );
      _updateJsonFromSections();
    });
  }

  void _removeSection(int index) {
    setState(() {
      _sections.removeAt(index);
      if (_editingSectionIndex == index) {
        _editingSectionIndex = null;
      } else if (_editingSectionIndex != null && _editingSectionIndex! > index) {
        _editingSectionIndex = _editingSectionIndex! - 1;
      }
      _updateJsonFromSections();
    });
  }

  void _updateJsonFromSections() {
    final jsonData = {
      'country': _countryController.text.trim(),
      'json_content': {
        'country': _countryController.text.trim(),
        'sections': _sections.map((section) => section.toJson()).toList(),
      }
    };

    _jsonController.text = const JsonEncoder.withIndent('  ').convert(jsonData);
  }

  void _updateSectionsFromJson() {
    try {
      final jsonData = jsonDecode(_jsonController.text);
      final jsonContent = jsonData['json_content'] as Map<String, dynamic>;

      _countryController.text = jsonData['country'] as String? ?? '';

      final sectionsJson = jsonContent['sections'] as List<dynamic>? ?? [];
      _sections = sectionsJson
          .map((sectionJson) => CountrySection.fromJson(sectionJson as Map<String, dynamic>))
          .toList();

      setState(() {});

      Fluttertoast.showToast(
        msg: "JSON başarıyla yüklendi",
        backgroundColor: Colors.green,
      );
    } catch (e) {
      Fluttertoast.showToast(
        msg: "JSON formatı hatalı: $e",
        backgroundColor: Colors.red,
      );
    }
  }

  void _importJsonFromClipboard() async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      if (clipboardData?.text != null) {
        _jsonController.text = clipboardData!.text!;
        _updateSectionsFromJson();
      }
    } catch (e) {
      Fluttertoast.showToast(
        msg: "Panoya erişim hatası: $e",
        backgroundColor: Colors.red,
      );
    }
  }

  void _exportJsonToClipboard() {
    _updateJsonFromSections();
    Clipboard.setData(ClipboardData(text: _jsonController.text));
    Fluttertoast.showToast(
      msg: "JSON panoya kopyalandı",
      backgroundColor: Colors.green,
    );
  }

  void _showIconPicker(BuildContext context, int index, CountrySection section) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: IconPickerWidget(
          currentIcon: section.icon,
          onIconSelected: (selectedIcon) {
            setState(() {
              _sections[index] = section.copyWith(icon: selectedIcon);
              _updateJsonFromSections();
            });
          },
        ),
      ),
    );
  }

  void _showImagePicker(BuildContext context, int index, CountrySection section) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: ImagePickerWidget(
          countryName: _countryController.text.isEmpty ? 'Ülke' : _countryController.text,
          currentImageUrl: section.imageUrl ?? '',
          onImageSelected: (selectedImageUrl) {
            setState(() {
              _sections[index] = section.copyWith(
                imageUrl: selectedImageUrl.isEmpty ? null : selectedImageUrl,
              );
              _updateJsonFromSections();
            });
          },
        ),
      ),
    );
  }

  Future<void> _saveCountryPage() async {
    try {
      print('Save başladı');

      // Ülke adı kontrolü
      final countryName = _countryController.text.trim();
      print('Country name: $countryName');

      if (countryName.isEmpty) {
        Fluttertoast.showToast(
          msg: "Ülke adı gereklidir",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
        );
        return;
      }
    } catch (e) {
      print('Save hatası: $e');
      Fluttertoast.showToast(
        msg: "Hata: $e",
        backgroundColor: Colors.red,
      );
      return;
    }

    if (_sections.isEmpty) {
      Fluttertoast.showToast(
        msg: "En az bir bölüm eklemelisiniz",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
      );
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final countryPage = CountryPage(
        id: _isEditing ? widget.countryPage!.id : '',
        country: _countryController.text.trim(),
        sections: _sections,
        createdAt: _isEditing ? widget.countryPage!.createdAt : DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (_isEditing) {
        await _countryPageService.updateCountryPage(
          widget.countryPage!.id,
          countryPage,
        );
        if (mounted) {
          Fluttertoast.showToast(
            msg: "Ülke bilgileri güncellendi",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM,
          );
        }
      } else {
        await _countryPageService.createCountryPage(countryPage);
        if (mounted) {
          Fluttertoast.showToast(
            msg: "Yeni ülke eklendi",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM,
          );
        }
      }

      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        Fluttertoast.showToast(
          msg: "Kaydetme işlemi başarısız: $e",
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Ülke Düzenle' : 'Yeni Ülke Ekle'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          if (!_isLoading) ...[
            IconButton(
              onPressed: _exportJsonToClipboard,
              icon: const Icon(Icons.copy),
              tooltip: 'JSON\'u Kopyala',
            ),
            IconButton(
              onPressed: _importJsonFromClipboard,
              icon: const Icon(Icons.paste),
              tooltip: 'JSON\'u Yapıştır',
            ),
            TextButton(
              onPressed: _isSaving ? null : _saveCountryPage,
              child: _isSaving
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Kaydet', style: TextStyle(color: Colors.white)),
            ),
          ],
        ],
        bottom: _isLoading ? null : TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.edit), text: 'Form'),
            Tab(icon: Icon(Icons.code), text: 'JSON'),
            Tab(icon: Icon(Icons.preview), text: 'Önizleme'),
          ],
        ),
      ),
      body: ResponsiveContainer(
        maxWidth: 1000,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _buildTabContent(),
      ),
    );
  }

  Widget _buildTabContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildFormTab(),
        _buildJsonTab(),
        _buildPreviewTab(),
      ],
    );
  }

  Widget _buildFormTab() {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Ülke adı
            _buildCountryNameField(),

            const SizedBox(height: 24),

            // Bölümler başlığı
            _buildSectionsHeader(),

            const SizedBox(height: 16),

            // Bölümler listesi
            ..._buildSectionsList(),

            const SizedBox(height: 24),

            // Kaydet butonu
            _buildSaveButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildJsonTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text(
                'JSON Editörü',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: _updateSectionsFromJson,
                icon: const Icon(Icons.refresh),
                label: const Text('JSON\'dan Yükle'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: TextField(
              controller: _jsonController,
              maxLines: null,
              expands: true,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'JSON verisini buraya yapıştırın...',
              ),
              style: const TextStyle(fontFamily: 'monospace'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _countryController.text.isEmpty ? 'Ülke Adı' : _countryController.text,
            style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          if (_sections.isEmpty)
            const Text('Henüz bölüm eklenmemiş')
          else
            ..._sections.map((section) => _buildPreviewSection(section)),
        ],
      ),
    );
  }

  Widget _buildPreviewSection(CountrySection section) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(section.icon, style: const TextStyle(fontSize: 24)),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    section.title.isEmpty ? 'Başlık Yok' : section.title,
                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ),
                Chip(
                  label: Text(section.category),
                  backgroundColor: Colors.blue.withOpacity(0.1),
                ),
              ],
            ),
            if (section.imageUrl != null && section.imageUrl!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Container(
                height: 200,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    section.imageUrl!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      color: Colors.grey.shade200,
                      child: const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.broken_image, size: 48, color: Colors.grey),
                            Text('Görsel yüklenemedi', style: TextStyle(color: Colors.grey)),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
            if (section.content.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(section.content),
            ],
            if (section.videoUrl != null && section.videoUrl!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.play_circle, color: Colors.red),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Video: ${section.videoUrl}',
                        style: const TextStyle(color: Colors.red),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            if (section.links.isNotEmpty) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Faydalı Linkler:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    ...section.links.map((link) => Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Row(
                        children: [
                          const Icon(Icons.link, size: 16, color: Colors.blue),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              '${link.label} (${link.url})',
                              style: const TextStyle(color: Colors.blue),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    )),
                  ],
                ),
              ),
            ],
            if (section.tags.isNotEmpty) ...[
              const SizedBox(height: 8),
              Wrap(
                spacing: 4,
                children: section.tags.map((tag) => Chip(
                  label: Text(tag, style: const TextStyle(fontSize: 12)),
                  backgroundColor: Colors.grey.withOpacity(0.2),
                )).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCountryNameField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Ülke Adı',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _countryController,
          decoration: InputDecoration(
            hintText: 'Örn: Hollanda, Almanya, Fransa',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            filled: true,
            fillColor: Colors.grey[50],
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Ülke adı gereklidir';
            }
            return null;
          },
          onChanged: (value) {
            _updateJsonFromSections();
          },
        ),
      ],
    );
  }

  Widget _buildSectionsHeader() {
    return Row(
      children: [
        const Text(
          'Bölümler',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        ElevatedButton.icon(
          onPressed: _addNewSection,
          icon: const Icon(Icons.add, size: 18),
          label: const Text('Bölüm Ekle'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
        ),
      ],
    );
  }

  List<Widget> _buildSectionsList() {
    return _sections.asMap().entries.map((entry) {
      final index = entry.key;
      final section = entry.value;
      
      return Container(
        margin: const EdgeInsets.only(bottom: 16),
        child: _buildSectionCard(index, section),
      );
    }).toList();
  }

  Widget _buildSectionCard(int index, CountrySection section) {
    final isExpanded = _editingSectionIndex == index;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Bölüm başlığı ve kontroller
            Row(
              children: [
                Text(
                  section.icon,
                  style: const TextStyle(fontSize: 20),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    section.title.isEmpty ? 'Bölüm ${index + 1}' : section.title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Chip(
                  label: Text(section.category),
                  backgroundColor: Colors.blue.withOpacity(0.1),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () {
                    setState(() {
                      _editingSectionIndex = isExpanded ? null : index;
                    });
                  },
                  icon: Icon(isExpanded ? Icons.expand_less : Icons.expand_more),
                  tooltip: isExpanded ? 'Daralt' : 'Genişlet',
                ),
                if (_sections.length > 1)
                  IconButton(
                    onPressed: () => _removeSection(index),
                    icon: const Icon(Icons.delete, color: Colors.red),
                    tooltip: 'Bölümü Sil',
                  ),
              ],
            ),

            if (isExpanded) ...[
              const SizedBox(height: 16),
              _buildSectionEditForm(index, section),
            ] else if (section.title.isNotEmpty || section.content.isNotEmpty) ...[
              const SizedBox(height: 8),
              if (section.content.isNotEmpty)
                Text(
                  section.content.length > 100
                      ? '${section.content.substring(0, 100)}...'
                      : section.content,
                  style: TextStyle(color: Colors.grey[600]),
                ),
              if (section.links.isNotEmpty) ...[
                const SizedBox(height: 4),
                Text(
                  '${section.links.length} link',
                  style: TextStyle(color: Colors.grey[500], fontSize: 12),
                ),
              ],
              if (section.tags.isNotEmpty) ...[
                const SizedBox(height: 4),
                Wrap(
                  spacing: 4,
                  children: section.tags.take(3).map((tag) => Chip(
                    label: Text(tag, style: const TextStyle(fontSize: 10)),
                    backgroundColor: Colors.grey.withOpacity(0.2),
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  )).toList(),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSectionEditForm(int index, CountrySection section) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Başlık
        TextFormField(
          initialValue: section.title,
          decoration: const InputDecoration(
            labelText: 'Başlık',
            border: OutlineInputBorder(),
          ),
          onChanged: (value) {
            _sections[index] = section.copyWith(title: value);
            _updateJsonFromSections();
          },
        ),

        const SizedBox(height: 16),

        // Kategori ve İkon
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _categories.contains(section.category) ? section.category : null,
                decoration: const InputDecoration(
                  labelText: 'Kategori',
                  border: OutlineInputBorder(),
                ),
                items: _categories.map((category) => DropdownMenuItem(
                  value: category,
                  child: Text(category),
                )).toList(),
                onChanged: (value) {
                  if (value != null) {
                    _sections[index] = section.copyWith(category: value);
                    _updateJsonFromSections();
                  }
                },
              ),
            ),
            const SizedBox(width: 16),
            SizedBox(
              width: 120,
              child: GestureDetector(
                onTap: () => _showIconPicker(context, index, section),
                child: Container(
                  height: 56,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        section.icon,
                        style: const TextStyle(fontSize: 24),
                      ),
                      const SizedBox(width: 8),
                      const Icon(Icons.arrow_drop_down),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // İçerik - Zengin Metin Editörü
        RichTextEditor(
          label: 'İçerik',
          initialText: section.content,
          minLines: 5,
          maxLines: 15,
          onTextChanged: (value) {
            _sections[index] = section.copyWith(content: value);
            _updateJsonFromSections();
          },
        ),

        const SizedBox(height: 16),

        // Görsel URL
        TextFormField(
          initialValue: section.imageUrl ?? '',
          decoration: InputDecoration(
            labelText: 'Görsel URL (Opsiyonel)',
            hintText: 'https://source.unsplash.com/featured/?netherlands',
            border: const OutlineInputBorder(),
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.auto_fix_high),
                  onPressed: () {
                    // Ülke adına göre uygun bir görsel seç
                    final countryName = _countryController.text.toLowerCase();
                    String photoId;

                    if (countryName.contains('hollanda') || countryName.contains('netherlands')) {
                      photoId = '1520637836862-4d197d17c90a'; // Amsterdam
                    } else if (countryName.contains('almanya') || countryName.contains('germany')) {
                      photoId = '1467269204594-9661b134dd2b'; // Berlin
                    } else if (countryName.contains('fransa') || countryName.contains('france')) {
                      photoId = '1502602898536-47ad22581b52'; // Paris
                    } else if (countryName.contains('ingiltere') || countryName.contains('england') || countryName.contains('uk')) {
                      photoId = '1513635269975-59663e0ac1ad'; // Londra
                    } else if (countryName.contains('ispanya') || countryName.contains('spain')) {
                      photoId = '1541963463532-d68292c34d19'; // Barcelona
                    } else {
                      photoId = '1506905925346-21bda4d32df4'; // Genel şehir manzarası
                    }

                    final autoUrl = 'https://images.unsplash.com/photo-$photoId?w=800&h=600&fit=crop&crop=center';
                    _sections[index] = section.copyWith(imageUrl: autoUrl);
                    _updateJsonFromSections();
                    setState(() {});

                    // Kullanıcıya bilgi ver
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('${_countryController.text} için uygun görsel eklendi'),
                        backgroundColor: Colors.green,
                        duration: const Duration(seconds: 2),
                      ),
                    );
                  },
                  tooltip: 'Ülkeye uygun görsel oluştur',
                ),
                IconButton(
                  icon: const Icon(Icons.photo_library),
                  onPressed: () => _showImagePicker(context, index, section),
                  tooltip: 'Görsel Seç',
                ),
              ],
            ),
          ),
          onChanged: (value) {
            _sections[index] = section.copyWith(imageUrl: value.isEmpty ? null : value);
            _updateJsonFromSections();
          },
        ),

        const SizedBox(height: 16),

        // Video URL
        TextFormField(
          initialValue: section.videoUrl ?? '',
          decoration: const InputDecoration(
            labelText: 'Video URL (Opsiyonel)',
            hintText: 'https://www.youtube.com/watch?v=...',
            border: OutlineInputBorder(),
          ),
          onChanged: (value) {
            _sections[index] = section.copyWith(videoUrl: value.isEmpty ? null : value);
            _updateJsonFromSections();
          },
        ),

        const SizedBox(height: 16),

        // Etiketler
        TextFormField(
          initialValue: section.tags.join(', '),
          decoration: const InputDecoration(
            labelText: 'Etiketler (virgülle ayırın)',
            hintText: 'konut, yaşam, hollanda',
            border: OutlineInputBorder(),
          ),
          onChanged: (value) {
            final tags = value.split(',').map((tag) => tag.trim()).where((tag) => tag.isNotEmpty).toList();
            _sections[index] = section.copyWith(tags: tags);
            _updateJsonFromSections();
          },
        ),

        const SizedBox(height: 16),

        // Faydalı Linkler
        _buildLinksSection(index, section),
      ],
    );
  }

  Widget _buildLinksSection(int index, CountrySection section) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'Faydalı Linkler',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            const Spacer(),
            IconButton(
              onPressed: () {
                final newLinks = List<CountryLink>.from(section.links);
                newLinks.add(CountryLink(label: '', url: ''));
                _sections[index] = section.copyWith(links: newLinks);
                _updateJsonFromSections();
                setState(() {});
              },
              icon: const Icon(Icons.add),
              tooltip: 'Link Ekle',
            ),
          ],
        ),

        if (section.links.isEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: const Center(
              child: Text(
                'Henüz link eklenmemiş. + butonuna tıklayarak link ekleyin.',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          )
        else
          ...section.links.asMap().entries.map((entry) {
            final linkIndex = entry.key;
            final link = entry.value;

            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: TextFormField(
                      initialValue: link.label,
                      decoration: const InputDecoration(
                        labelText: 'Link Başlığı',
                        border: OutlineInputBorder(),
                        isDense: true,
                      ),
                      onChanged: (value) {
                        final newLinks = List<CountryLink>.from(section.links);
                        newLinks[linkIndex] = CountryLink(label: value, url: link.url);
                        _sections[index] = section.copyWith(links: newLinks);
                        _updateJsonFromSections();
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    flex: 3,
                    child: TextFormField(
                      initialValue: link.url,
                      decoration: const InputDecoration(
                        labelText: 'URL',
                        border: OutlineInputBorder(),
                        isDense: true,
                      ),
                      onChanged: (value) {
                        final newLinks = List<CountryLink>.from(section.links);
                        newLinks[linkIndex] = CountryLink(label: link.label, url: value);
                        _sections[index] = section.copyWith(links: newLinks);
                        _updateJsonFromSections();
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: () {
                      final newLinks = List<CountryLink>.from(section.links);
                      newLinks.removeAt(linkIndex);
                      _sections[index] = section.copyWith(links: newLinks);
                      _updateJsonFromSections();
                      setState(() {});
                    },
                    icon: const Icon(Icons.delete, color: Colors.red),
                    tooltip: 'Linki Sil',
                  ),
                ],
              ),
            );
          }).toList(),
      ],
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isSaving ? null : _saveCountryPage,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: _isSaving
            ? const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  SizedBox(width: 8),
                  Text('Kaydediliyor...'),
                ],
              )
            : Text(_isEditing ? 'Güncelle' : 'Kaydet'),
      ),
    );
  }
}
