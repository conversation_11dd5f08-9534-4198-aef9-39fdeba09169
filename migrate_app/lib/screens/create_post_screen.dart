import 'package:flutter/material.dart';
import '../models/post_model.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';
import '../services/post_service.dart';
import '../widgets/responsive_container.dart';

class CreatePostScreen extends StatefulWidget {
  final PostType? initialType;
  
  const CreatePostScreen({
    Key? key,
    this.initialType,
  }) : super(key: key);

  @override
  State<CreatePostScreen> createState() => _CreatePostScreenState();
}

class _CreatePostScreenState extends State<CreatePostScreen> {
  final AuthService _authService = AuthService();
  final PostService _postService = PostService();
  final _formKey = GlobalKey<FormState>();
  
  AppUser? _currentUser;
  PostType _selectedType = PostType.experience;
  
  // Form controllers
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  final _tagsController = TextEditingController();
  
  // Type-specific controllers
  final _countryController = TextEditingController();
  final _cityController = TextEditingController();
  final _categoryController = TextEditingController();
  final _companyController = TextEditingController();
  final _positionController = TextEditingController();
  
  bool _isLoading = false;
  List<String> _selectedTags = [];
  List<String> _images = [];

  @override
  void initState() {
    super.initState();
    if (widget.initialType != null) {
      _selectedType = widget.initialType!;
    }
    _loadUserData();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _tagsController.dispose();
    _countryController.dispose();
    _cityController.dispose();
    _categoryController.dispose();
    _companyController.dispose();
    _positionController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    try {
      final user = await _authService.getCurrentUserData();
      setState(() {
        _currentUser = user;
      });
    } catch (e) {
      print('Kullanıcı verisi yüklenemedi: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        title: Text(
          'Yeni ${_selectedType.label}',
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: ElevatedButton(
              onPressed: _isLoading ? null : _submitPost,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : const Text('Paylaş'),
            ),
          ),
        ],
      ),
      body: ResponsiveContainer(
        maxWidth: 800,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Post Type Selector
                _buildPostTypeSelector(),
                
                const SizedBox(height: 20),
                
                // User Info
                _buildUserInfo(),
                
                const SizedBox(height: 20),
                
                // Title
                _buildTitleField(),
                
                const SizedBox(height: 16),
                
                // Content
                _buildContentField(),
                
                const SizedBox(height: 20),
                
                // Type-specific fields
                _buildTypeSpecificFields(),
                
                const SizedBox(height: 20),
                
                // Tags
                _buildTagsField(),
                
                const SizedBox(height: 20),
                
                // Media
                _buildMediaSection(),
                
                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPostTypeSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Post Türü',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: PostType.values.map((type) {
              final isSelected = _selectedType == type;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedType = type;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected ? Colors.blue : Colors.grey[100],
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected ? Colors.blue : Colors.grey[300]!,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        type.emoji,
                        style: const TextStyle(fontSize: 16),
                      ),
                      const SizedBox(width: 6),
                      Text(
                        type.label,
                        style: TextStyle(
                          color: isSelected ? Colors.white : Colors.grey[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildUserInfo() {
    if (_currentUser == null) return const SizedBox.shrink();
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 24,
            backgroundColor: Colors.blue,
            child: Text(
              _currentUser!.email.substring(0, 1).toUpperCase(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _currentUser!.email.split('@')[0],
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const Text(
                  'Türkiye → Hollanda • Yazılım Geliştirici',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTitleField() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: _titleController,
        decoration: InputDecoration(
          labelText: _getTitleLabel(),
          hintText: _getTitleHint(),
          border: const OutlineInputBorder(),
        ),
        validator: (value) {
          if (value == null || value.trim().isEmpty) {
            return 'Başlık gerekli';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildContentField() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: _contentController,
        maxLines: 6,
        decoration: InputDecoration(
          labelText: _getContentLabel(),
          hintText: _getContentHint(),
          border: const OutlineInputBorder(),
        ),
        validator: (value) {
          if (value == null || value.trim().isEmpty) {
            return 'İçerik gerekli';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildTypeSpecificFields() {
    switch (_selectedType) {
      case PostType.experience:
        return _buildExperienceFields();
      case PostType.question:
        return _buildQuestionFields();
      case PostType.event:
        return _buildEventFields();
      case PostType.job:
        return _buildJobFields();
      case PostType.success:
        return _buildSuccessFields();
    }
  }

  Widget _buildExperienceFields() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Deneyim Detayları',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _countryController,
                  decoration: const InputDecoration(
                    labelText: 'Ülke',
                    hintText: 'Hollanda',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: TextFormField(
                  controller: _cityController,
                  decoration: const InputDecoration(
                    labelText: 'Şehir',
                    hintText: 'Amsterdam',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionFields() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Soru Detayları',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            decoration: const InputDecoration(
              labelText: 'Kategori',
              border: OutlineInputBorder(),
            ),
            items: const [
              DropdownMenuItem(value: 'vize', child: Text('Vize & Göç')),
              DropdownMenuItem(value: 'konut', child: Text('Konut')),
              DropdownMenuItem(value: 'iş', child: Text('İş & Kariyer')),
              DropdownMenuItem(value: 'eğitim', child: Text('Eğitim')),
              DropdownMenuItem(value: 'sağlık', child: Text('Sağlık')),
              DropdownMenuItem(value: 'diğer', child: Text('Diğer')),
            ],
            onChanged: (value) {
              _categoryController.text = value ?? '';
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEventFields() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Etkinlik Detayları',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16),
          Text('Etkinlik alanları yakında eklenecek...'),
        ],
      ),
    );
  }

  Widget _buildJobFields() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'İş İlanı Detayları',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _companyController,
                  decoration: const InputDecoration(
                    labelText: 'Şirket',
                    hintText: 'ABC Teknoloji',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: TextFormField(
                  controller: _positionController,
                  decoration: const InputDecoration(
                    labelText: 'Pozisyon',
                    hintText: 'Frontend Developer',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessFields() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Başarı Detayları',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16),
          Text('Başarı alanları yakında eklenecek...'),
        ],
      ),
    );
  }

  Widget _buildTagsField() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Etiketler',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          TextFormField(
            controller: _tagsController,
            decoration: const InputDecoration(
              labelText: 'Etiketler (virgülle ayırın)',
              hintText: 'hollanda, amsterdam, vize, iş',
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              setState(() {
                _selectedTags = value
                    .split(',')
                    .map((tag) => tag.trim())
                    .where((tag) => tag.isNotEmpty)
                    .toList();
              });
            },
          ),
          if (_selectedTags.isNotEmpty) ...[
            const SizedBox(height: 12),
            Wrap(
              spacing: 6,
              runSpacing: 6,
              children: _selectedTags.map((tag) {
                return Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '#$tag',
                    style: const TextStyle(
                      color: Colors.blue,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMediaSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Medya',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    // TODO: Implement image picker
                  },
                  icon: const Icon(Icons.photo),
                  label: const Text('Fotoğraf Ekle'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    // TODO: Implement video picker
                  },
                  icon: const Icon(Icons.videocam),
                  label: const Text('Video Ekle'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getTitleLabel() {
    switch (_selectedType) {
      case PostType.experience:
        return 'Deneyim Başlığı';
      case PostType.question:
        return 'Soru Başlığı';
      case PostType.event:
        return 'Etkinlik Adı';
      case PostType.job:
        return 'İş İlanı Başlığı';
      case PostType.success:
        return 'Başarı Başlığı';
    }
  }

  String _getTitleHint() {
    switch (_selectedType) {
      case PostType.experience:
        return 'Amsterdam\'da ev bulma deneyimim';
      case PostType.question:
        return 'Hollanda vize başvurusu nasıl yapılır?';
      case PostType.event:
        return 'Amsterdam Türk Buluşması';
      case PostType.job:
        return 'Frontend Developer - Amsterdam';
      case PostType.success:
        return 'Hollanda\'da iş buldum! 🎉';
    }
  }

  String _getContentLabel() {
    switch (_selectedType) {
      case PostType.experience:
        return 'Deneyiminizi Paylaşın';
      case PostType.question:
        return 'Sorunuzu Detaylandırın';
      case PostType.event:
        return 'Etkinlik Açıklaması';
      case PostType.job:
        return 'İş Açıklaması';
      case PostType.success:
        return 'Başarı Hikayeniz';
    }
  }

  String _getContentHint() {
    switch (_selectedType) {
      case PostType.experience:
        return 'Amsterdam\'da ev arama sürecim 3 ay sürdü. İşte yaşadıklarım...';
      case PostType.question:
        return 'Hollanda\'ya taşınmayı planlıyorum ama vize süreci hakkında...';
      case PostType.event:
        return 'Amsterdam\'daki Türk topluluğu olarak bir araya geliyoruz...';
      case PostType.job:
        return 'Şirketimiz deneyimli bir Frontend Developer arıyor...';
      case PostType.success:
        return '6 aylık arama sonunda harika bir şirkette işe başladım...';
    }
  }

  Future<void> _submitPost() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Metadata oluştur
      Map<String, dynamic>? metadata;

      switch (_selectedType) {
        case PostType.experience:
          if (_countryController.text.isNotEmpty || _cityController.text.isNotEmpty) {
            metadata = {
              'country': _countryController.text,
              'city': _cityController.text,
              'topics': [],
              'rating': 5,
            };
          }
          break;
        case PostType.question:
          if (_categoryController.text.isNotEmpty) {
            metadata = {
              'category': _categoryController.text,
              'urgency': 'orta',
              'is_answered': false,
            };
          }
          break;
        case PostType.job:
          if (_companyController.text.isNotEmpty || _positionController.text.isNotEmpty) {
            metadata = {
              'company': _companyController.text,
              'position': _positionController.text,
              'job_type': 'tam-zamanlı',
              'experience_level': 'mid',
              'salary_range': '',
              'requirements': [],
              'application_url': '',
            };
          }
          break;
        default:
          break;
      }

      // Post oluştur
      final post = await _postService.createPost(
        title: _titleController.text.trim(),
        content: _contentController.text.trim(),
        type: _selectedType,
        tags: _selectedTags,
        images: _images,
        metadata: metadata,
      );

      if (mounted) {
        Navigator.pop(context, post); // Post objesini geri döndür
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${_selectedType.label} başarıyla paylaşıldı!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Hata: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
