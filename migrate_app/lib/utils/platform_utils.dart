import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';

class PlatformUtils {
  /// iOS platformunda mı kontrol eder
  static bool get isIOS {
    if (kIsWeb) return false;
    return Platform.isIOS;
  }

  /// Android platformunda mı kontrol eder
  static bool get isAndroid {
    if (kIsWeb) return false;
    return Platform.isAndroid;
  }

  /// Web platformunda mı kontrol eder
  static bool get isWeb => kIsWeb;

  /// Mobile platformda mı kontrol eder (iOS veya Android)
  static bool get isMobile => isIOS || isAndroid;

  /// Platform-aware haptic feedback
  static void hapticFeedback({HapticFeedbackType type = HapticFeedbackType.medium}) {
    if (isMobile) {
      switch (type) {
        case HapticFeedbackType.light:
          HapticFeedback.lightImpact();
          break;
        case HapticFeedbackType.medium:
          HapticFeedback.mediumImpact();
          break;
        case HapticFeedbackType.heavy:
          HapticFeedback.heavyImpact();
          break;
        case HapticFeedbackType.selection:
          HapticFeedback.selectionClick();
          break;
      }
    }
  }

  /// Platform-aware page route
  static PageRoute<T> createRoute<T>(Widget page, {String? routeName}) {
    if (isIOS) {
      return CupertinoPageRoute<T>(
        builder: (context) => page,
        settings: routeName != null ? RouteSettings(name: routeName) : null,
      );
    } else {
      return MaterialPageRoute<T>(
        builder: (context) => page,
        settings: routeName != null ? RouteSettings(name: routeName) : null,
      );
    }
  }

  /// Platform-aware navigation
  static Future<T?> pushPage<T>(BuildContext context, Widget page, {String? routeName}) {
    return Navigator.push<T>(
      context,
      createRoute<T>(page, routeName: routeName),
    );
  }

  /// Platform-aware colors
  static Color getPrimaryColor(BuildContext context) {
    if (isIOS) {
      return CupertinoColors.activeBlue;
    } else {
      return Theme.of(context).primaryColor;
    }
  }

  static Color getSecondaryColor(BuildContext context) {
    if (isIOS) {
      return CupertinoColors.systemGrey;
    } else {
      return Theme.of(context).colorScheme.secondary;
    }
  }

  static Color getBackgroundColor(BuildContext context) {
    if (isIOS) {
      return CupertinoColors.systemBackground;
    } else {
      return Theme.of(context).scaffoldBackgroundColor;
    }
  }

  static Color getCardColor(BuildContext context) {
    if (isIOS) {
      return CupertinoColors.secondarySystemBackground;
    } else {
      return Theme.of(context).cardColor;
    }
  }

  static Color getTextColor(BuildContext context) {
    if (isIOS) {
      return CupertinoColors.label;
    } else {
      return Theme.of(context).textTheme.bodyLarge?.color ?? Colors.black;
    }
  }

  static Color getSecondaryTextColor(BuildContext context) {
    if (isIOS) {
      return CupertinoColors.secondaryLabel;
    } else {
      return Theme.of(context).textTheme.bodyMedium?.color ?? Colors.grey;
    }
  }
}

enum HapticFeedbackType {
  light,
  medium,
  heavy,
  selection,
}
