import 'package:supabase_flutter/supabase_flutter.dart';

class AdminHelper {
  static final SupabaseClient _supabase = Supabase.instance.client;

  // Test admin kullanıcısı oluştur
  static Future<void> createTestAdmin() async {
    // Test kullanıcısı bilgileri
    const testEmail = '<EMAIL>';
    const testPassword = 'admin123';
    const testCity = 'İstanbul';
    const testCountry = 'Türkiye';

    try {
      print('AdminHelper: Test admin kullanıcısı oluşturuluyor...');

      // 1. Kullanıcı kaydı yap
      final AuthResponse response = await _supabase.auth.signUp(
        email: testEmail,
        password: testPassword,
      );

      if (response.user != null) {
        print('AdminHelper: Kullanıcı oluşturuldu - ID: ${response.user!.id}');

        // 2. Users tablosuna ekle
        await _supabase.from('users').insert({
          'id': response.user!.id,
          'email': testEmail,
          'city': testCity,
          'country': testCountry,
          'created_at': DateTime.now().toIso8601String(),
        });

        print('AdminHelper: Users tablosuna eklendi');

        // 3. Admins tablosuna ekle
        await _supabase.from('admins').insert({
          'user_id': response.user!.id,
        });

        print('AdminHelper: Admins tablosuna eklendi');
        print('AdminHelper: Test admin kullanıcısı başarıyla oluşturuldu!');
        print('Email: $testEmail');
        print('Şifre: $testPassword');
      } else {
        print('AdminHelper: Kullanıcı oluşturulamadı');
      }
    } catch (e) {
      print('AdminHelper: Hata oluştu - $e');

      // Eğer kullanıcı zaten varsa, sadece admin yetkisi ver
      if (e.toString().contains('User already registered')) {
        print('AdminHelper: Kullanıcı zaten mevcut, admin yetkisi veriliyor...');
        await _makeExistingUserAdmin(testEmail);
      }
    }
  }

  // Mevcut kullanıcıyı admin yap
  static Future<void> _makeExistingUserAdmin(String email) async {
    try {
      // Önce giriş yap
      await _supabase.auth.signInWithPassword(
        email: email,
        password: 'admin123',
      );

      final user = _supabase.auth.currentUser;
      if (user != null) {
        // Admin tablosuna ekle (eğer yoksa)
        await _supabase.from('admins').upsert({
          'user_id': user.id,
        });

        print('AdminHelper: Mevcut kullanıcı admin yapıldı - ID: ${user.id}');
      }
    } catch (e) {
      print('AdminHelper: Mevcut kullanıcıyı admin yaparken hata - $e');
    }
  }

  // Şu anda giriş yapmış kullanıcıyı admin yap
  static Future<bool> makeCurrentUserAdmin() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        print('AdminHelper: Giriş yapmış kullanıcı bulunamadı');
        return false;
      }

      print('AdminHelper: Mevcut kullanıcı admin yapılıyor - ID: ${user.id}');

      // Admin tablosuna ekle (eğer yoksa)
      await _supabase.from('admins').upsert({
        'user_id': user.id,
      });

      print('AdminHelper: Mevcut kullanıcı başarıyla admin yapıldı!');
      return true;
    } catch (e) {
      print('AdminHelper: Mevcut kullanıcıyı admin yaparken hata - $e');
      return false;
    }
  }

  // Admin durumunu kontrol et
  static Future<bool> checkAdminStatus(String userId) async {
    try {
      final response = await _supabase
          .from('admins')
          .select()
          .eq('user_id', userId)
          .maybeSingle();

      return response != null;
    } catch (e) {
      print('AdminHelper: Admin durumu kontrol edilirken hata - $e');
      return false;
    }
  }

  // Tüm adminleri listele
  static Future<void> listAllAdmins() async {
    try {
      final admins = await _supabase
          .from('admins')
          .select('user_id')
          .order('user_id');

      print('AdminHelper: Toplam admin sayısı: ${admins.length}');
      for (var admin in admins) {
        print('Admin User ID: ${admin['user_id']}');
      }
    } catch (e) {
      print('AdminHelper: Adminler listelenirken hata - $e');
    }
  }

  // Test verileri oluştur
  static Future<void> createTestData() async {
    try {
      print('AdminHelper: Test verileri oluşturuluyor...');

      // Örnek ülke sayfası oluştur
      final testCountryData = {
        'country': 'Almanya',
        'json_content': {
          'sections': [
            {
              'id': 'life',
              'title': 'Yaşam',
              'description': 'Almanya\'da yaşam hakkında bilgiler',
              'content': [
                {
                  'type': 'text',
                  'content': 'Almanya\'da yaşam kalitesi oldukça yüksektir.'
                },
                {
                  'type': 'link',
                  'title': 'Almanya Yaşam Rehberi',
                  'url': 'https://example.com/almanya-yasam',
                  'description': 'Detaylı yaşam rehberi'
                }
              ]
            },
            {
              'id': 'migration',
              'title': 'Göç',
              'description': 'Almanya\'ya göç süreci',
              'content': [
                {
                  'type': 'text',
                  'content': 'Almanya\'ya göç için gerekli belgeler ve süreçler.'
                }
              ]
            }
          ]
        }
      };

      await _supabase.from('country_pages').insert(testCountryData);
      print('AdminHelper: Test ülke sayfası oluşturuldu');

    } catch (e) {
      print('AdminHelper: Test verileri oluşturulurken hata - $e');
    }
  }
}
