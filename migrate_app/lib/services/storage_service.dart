import 'dart:typed_data';
import 'package:supabase_flutter/supabase_flutter.dart';

class StorageService {
  final SupabaseClient _supabase = Supabase.instance.client;

  // Profil fotoğrafı yükle
  Future<String> uploadProfilePhoto(Uint8List fileBytes, String fileName) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('Kullanıcı giriş yapmamış');
      }

      // Dosya adını benzersiz yap
      final uniqueFileName = '${user.id}_${DateTime.now().millisecondsSinceEpoch}_$fileName';
      
      // Dosyayı profile-photos bucket'ına yükle
      await _supabase.storage
          .from('profile-photos')
          .uploadBinary(
            uniqueFileName,
            fileBytes,
            fileOptions: const FileOptions(
              cacheControl: '3600',
              upsert: true, // Aynı dosya varsa üzerine yaz
            ),
          );

      // Public URL'i al
      final publicUrl = _supabase.storage
          .from('profile-photos')
          .getPublicUrl(uniqueFileName);

      return publicUrl;
    } catch (e) {
      print('Profil fotoğrafı yükleme hatası: $e');
      throw Exception('Profil fotoğrafı yüklenemedi: $e');
    }
  }

  // Eski profil fotoğrafını sil
  Future<void> deleteProfilePhoto(String photoUrl) async {
    try {
      // URL'den dosya adını çıkar
      final uri = Uri.parse(photoUrl);
      final fileName = uri.pathSegments.last;
      
      // Dosyayı sil
      await _supabase.storage
          .from('profile-photos')
          .remove([fileName]);
    } catch (e) {
      print('Profil fotoğrafı silme hatası: $e');
      // Silme hatası kritik değil, devam et
    }
  }

  // Event fotoğrafı yükle
  Future<String> uploadEventPhoto(Uint8List fileBytes, String fileName) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('Kullanıcı giriş yapmamış');
      }

      // Dosya adını benzersiz yap
      final uniqueFileName = '${user.id}_${DateTime.now().millisecondsSinceEpoch}_$fileName';
      
      // Dosyayı event-images bucket'ına yükle
      await _supabase.storage
          .from('event-images')
          .uploadBinary(
            uniqueFileName,
            fileBytes,
            fileOptions: const FileOptions(
              cacheControl: '3600',
              upsert: true,
            ),
          );

      // Public URL'i al
      final publicUrl = _supabase.storage
          .from('event-images')
          .getPublicUrl(uniqueFileName);

      return publicUrl;
    } catch (e) {
      print('Event fotoğrafı yükleme hatası: $e');
      throw Exception('Event fotoğrafı yüklenemedi: $e');
    }
  }

  // Post fotoğrafı yükle
  Future<String> uploadPostPhoto(Uint8List fileBytes, String fileName) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('Kullanıcı giriş yapmamış');
      }

      // Dosya adını benzersiz yap
      final uniqueFileName = '${user.id}_${DateTime.now().millisecondsSinceEpoch}_$fileName';
      
      // Dosyayı post-images bucket'ına yükle
      await _supabase.storage
          .from('post-images')
          .uploadBinary(
            uniqueFileName,
            fileBytes,
            fileOptions: const FileOptions(
              cacheControl: '3600',
              upsert: true,
            ),
          );

      // Public URL'i al
      final publicUrl = _supabase.storage
          .from('post-images')
          .getPublicUrl(uniqueFileName);

      return publicUrl;
    } catch (e) {
      print('Post fotoğrafı yükleme hatası: $e');
      throw Exception('Post fotoğrafı yüklenemedi: $e');
    }
  }

  // Bucket'ların var olup olmadığını kontrol et ve oluştur
  Future<void> ensureBucketsExist() async {
    try {
      final buckets = ['profile-photos', 'event-images', 'post-images'];
      
      for (final bucketName in buckets) {
        try {
          // Bucket'ı kontrol et
          await _supabase.storage.getBucket(bucketName);
        } catch (e) {
          // Bucket yoksa oluştur
          try {
            await _supabase.storage.createBucket(
              bucketName,
              BucketOptions(
                public: true,
                allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp'],
                fileSizeLimit: '5MB',
              ),
            );
            print('Bucket oluşturuldu: $bucketName');
          } catch (createError) {
            print('Bucket oluşturma hatası ($bucketName): $createError');
          }
        }
      }
    } catch (e) {
      print('Bucket kontrol hatası: $e');
    }
  }
}
