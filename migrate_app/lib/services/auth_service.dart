import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/material.dart';
import '../models/user_model.dart';

class AuthService {
  final SupabaseClient _supabase = Supabase.instance.client;

  // Get current user
  User? get currentUser => _supabase.auth.currentUser;

  // Check if user is logged in
  bool get isLoggedIn => currentUser != null;

  // Sign up with email and password
  Future<void> signUp({
    required String email,
    required String password,
    required String city,
    required String country,
    required BuildContext context,
  }) async {
    try {
      // Sign up the user
      final AuthResponse response = await _supabase.auth.signUp(
        email: email,
        password: password,
      );

      if (response.user != null) {
        // Add user data to the users table
        await _supabase.from('users').insert({
          'id': response.user!.id,
          'email': email,
          'city': city,
          'country': country,
          'created_at': DateTime.now().toIso8601String(),
        });
      }
    } catch (e) {
      rethrow;
    }
  }

  // Sign in with email and password
  Future<void> signIn({
    required String email,
    required String password,
  }) async {
    try {
      print('AuthService: Supabase giriş işlemi başlatılıyor - Email: $email');
      final response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );
      print('AuthService: Supabase giriş başarılı - User: ${response.user?.id}');
    } catch (e) {
      print('AuthService: Supabase giriş hatası - $e');
      rethrow;
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _supabase.auth.signOut();
    } catch (e) {
      rethrow;
    }
  }

  // Check if user is admin
  Future<bool> isAdmin() async {
    try {
      if (currentUser == null) return false;

      final response = await _supabase
          .from('admins')
          .select()
          .eq('user_id', currentUser!.id)
          .single();

      return response != null;
    } catch (e) {
      return false;
    }
  }

  // Get current user data
  Future<AppUser?> getCurrentUserData() async {
    try {
      if (currentUser == null) return null;

      final userData = await _supabase
          .from('users')
          .select()
          .eq('id', currentUser!.id)
          .single();

      final isUserAdmin = await isAdmin();

      return AppUser.fromJson(userData, isAdmin: isUserAdmin);
    } catch (e) {
      return null;
    }
  }

  // Reset password
  Future<void> resetPassword({required String email}) async {
    try {
      await _supabase.auth.resetPasswordForEmail(
        email,
        redirectTo: 'https://ejrwgegahbinadynqpul.supabase.co/auth/v1/verify',
      );
    } catch (e) {
      rethrow;
    }
  }

  // Update password
  Future<void> updatePassword({required String newPassword}) async {
    try {
      await _supabase.auth.updateUser(
        UserAttributes(
          password: newPassword,
        ),
      );
    } catch (e) {
      rethrow;
    }
  }
}
