import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_profile_model.dart';

class UserProfileService {
  final SupabaseClient _supabase = Supabase.instance.client;

  // Kullanıcı profilini getir
  Future<UserProfile?> getUserProfile(String userId) async {
    try {
      final response = await _supabase
          .from('user_profiles')
          .select()
          .eq('user_id', userId)
          .maybeSingle();

      if (response == null) return null;
      return UserProfile.fromJson(response);
    } catch (e) {
      print('Profil getirme hatası: $e');
      throw Exception('Profil getirilemedi: $e');
    }
  }

  // Mevcut kullanıcının profilini getir
  Future<UserProfile?> getCurrentUserProfile() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return null;

      return await getUserProfile(user.id);
    } catch (e) {
      print('Mevcut kullanıcı profili getirme hatası: $e');
      throw Exception('Profil getirilemedi: $e');
    }
  }

  // Profil oluştur
  Future<UserProfile> createProfile({
    required String firstName,
    required String lastName,
    String? bio,
    String? avatar,
    required String fromCountry,
    required String fromCity,
    required String toCountry,
    required String toCity,
    DateTime? migrationDate,
    String migrationStatus = 'planning',
    required String profession,
    required String industry,
    String experienceLevel = 'mid',
    List<String> skills = const [],
    String? currentCompany,
    String? currentPosition,
    List<String> helpTopics = const [],
    List<String> userCategories = const [],
    List<String> networkingGoals = const [],
    bool isOpenToMentoring = false,
    bool isSeekingMentor = false,
    bool isOpenToNetworking = true,
    List<String> preferredLanguages = const ['tr'],
    String? linkedinUrl,
    String? twitterUrl,
    String? instagramUrl,
    String? websiteUrl,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('Kullanıcı giriş yapmamış');
      }

      final profileData = {
        'user_id': user.id,
        'email': user.email!,
        'first_name': firstName,
        'last_name': lastName,
        'bio': bio,
        'avatar': avatar,
        'from_country': fromCountry,
        'from_city': fromCity,
        'to_country': toCountry,
        'to_city': toCity,
        'migration_date': migrationDate?.toIso8601String(),
        'migration_status': migrationStatus,
        'profession': profession,
        'industry': industry,
        'experience_level': experienceLevel,
        'skills': skills,
        'current_company': currentCompany,
        'current_position': currentPosition,
        'help_topics': helpTopics,
        'user_categories': userCategories,
        'networking_goals': networkingGoals,
        'is_open_to_mentoring': isOpenToMentoring,
        'is_seeking_mentor': isSeekingMentor,
        'is_open_to_networking': isOpenToNetworking,
        'preferred_languages': preferredLanguages,
        'linkedin_url': linkedinUrl,
        'twitter_url': twitterUrl,
        'instagram_url': instagramUrl,
        'website_url': websiteUrl,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        'is_verified': false,
        'is_public': true,
      };

      final response = await _supabase
          .from('user_profiles')
          .insert(profileData)
          .select()
          .single();

      return UserProfile.fromJson(response);
    } catch (e) {
      print('Profil oluşturma hatası: $e');
      throw Exception('Profil oluşturulamadı: $e');
    }
  }

  // Profil güncelle
  Future<UserProfile> updateProfile({
    String? firstName,
    String? lastName,
    String? bio,
    String? avatar,
    String? fromCountry,
    String? fromCity,
    String? toCountry,
    String? toCity,
    DateTime? migrationDate,
    String? migrationStatus,
    String? profession,
    String? industry,
    String? experienceLevel,
    List<String>? skills,
    String? currentCompany,
    String? currentPosition,
    List<String>? helpTopics,
    List<String>? userCategories,
    List<String>? networkingGoals,
    bool? isOpenToMentoring,
    bool? isSeekingMentor,
    bool? isOpenToNetworking,
    List<String>? preferredLanguages,
    String? linkedinUrl,
    String? twitterUrl,
    String? instagramUrl,
    String? websiteUrl,
    bool? isPublic,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('Kullanıcı giriş yapmamış');
      }

      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (firstName != null) updateData['first_name'] = firstName;
      if (lastName != null) updateData['last_name'] = lastName;
      if (bio != null) updateData['bio'] = bio;
      if (avatar != null) updateData['avatar'] = avatar;
      if (fromCountry != null) updateData['from_country'] = fromCountry;
      if (fromCity != null) updateData['from_city'] = fromCity;
      if (toCountry != null) updateData['to_country'] = toCountry;
      if (toCity != null) updateData['to_city'] = toCity;
      if (migrationDate != null) updateData['migration_date'] = migrationDate.toIso8601String();
      if (migrationStatus != null) updateData['migration_status'] = migrationStatus;
      if (profession != null) updateData['profession'] = profession;
      if (industry != null) updateData['industry'] = industry;
      if (experienceLevel != null) updateData['experience_level'] = experienceLevel;
      if (skills != null) updateData['skills'] = skills;
      if (currentCompany != null) updateData['current_company'] = currentCompany;
      if (currentPosition != null) updateData['current_position'] = currentPosition;
      if (helpTopics != null) updateData['help_topics'] = helpTopics;
      if (userCategories != null) updateData['user_categories'] = userCategories;
      if (networkingGoals != null) updateData['networking_goals'] = networkingGoals;
      if (isOpenToMentoring != null) updateData['is_open_to_mentoring'] = isOpenToMentoring;
      if (isSeekingMentor != null) updateData['is_seeking_mentor'] = isSeekingMentor;
      if (isOpenToNetworking != null) updateData['is_open_to_networking'] = isOpenToNetworking;
      if (preferredLanguages != null) updateData['preferred_languages'] = preferredLanguages;
      if (linkedinUrl != null) updateData['linkedin_url'] = linkedinUrl;
      if (twitterUrl != null) updateData['twitter_url'] = twitterUrl;
      if (instagramUrl != null) updateData['instagram_url'] = instagramUrl;
      if (websiteUrl != null) updateData['website_url'] = websiteUrl;
      if (isPublic != null) updateData['is_public'] = isPublic;

      final response = await _supabase
          .from('user_profiles')
          .update(updateData)
          .eq('user_id', user.id)
          .select()
          .single();

      return UserProfile.fromJson(response);
    } catch (e) {
      print('Profil güncelleme hatası: $e');
      throw Exception('Profil güncellenemedi: $e');
    }
  }

  // Benzer profilleri getir (networking önerileri)
  Future<List<UserProfile>> getSuggestedProfiles({
    int limit = 10,
    String? targetCountry,
    String? profession,
    List<String>? helpTopics,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return [];

      var query = _supabase
          .from('user_profiles')
          .select()
          .neq('user_id', user.id) // Kendi profilini hariç tut
          .eq('is_public', true)
          .eq('is_open_to_networking', true)
          .limit(limit);

      final response = await query;
      return response.map<UserProfile>((json) => UserProfile.fromJson(json)).toList();
    } catch (e) {
      print('Önerilen profiller getirme hatası: $e');
      return []; // Hata durumunda boş liste döndür
    }
  }

  // Mentor arayan kullanıcıları getir
  Future<List<UserProfile>> getMentorSeekers({
    int limit = 10,
    String? targetCountry,
    String? profession,
  }) async {
    try {
      final response = await _supabase
          .from('user_profiles')
          .select()
          .eq('is_seeking_mentor', true)
          .eq('is_public', true)
          .limit(limit);

      return response.map<UserProfile>((json) => UserProfile.fromJson(json)).toList();
    } catch (e) {
      print('Mentor arayan kullanıcılar getirme hatası: $e');
      return [];
    }
  }

  // Mentor olabilecek kullanıcıları getir
  Future<List<UserProfile>> getPotentialMentors({
    int limit = 10,
    String? targetCountry,
    String? profession,
  }) async {
    try {
      final response = await _supabase
          .from('user_profiles')
          .select()
          .eq('is_open_to_mentoring', true)
          .eq('is_public', true)
          .limit(limit);

      return response.map<UserProfile>((json) => UserProfile.fromJson(json)).toList();
    } catch (e) {
      print('Potansiyel mentorlar getirme hatası: $e');
      return [];
    }
  }

  // Ülkeye göre kullanıcıları getir
  Future<List<UserProfile>> getUsersByCountry(String country, {int limit = 20}) async {
    try {
      final response = await _supabase
          .from('user_profiles')
          .select()
          .eq('is_public', true)
          .limit(limit);

      return response.map<UserProfile>((json) => UserProfile.fromJson(json)).toList();
    } catch (e) {
      print('Ülkeye göre kullanıcılar getirme hatası: $e');
      return [];
    }
  }

  // Mesleğe göre kullanıcıları getir
  Future<List<UserProfile>> getUsersByProfession(String profession, {int limit = 20}) async {
    try {
      final response = await _supabase
          .from('user_profiles')
          .select()
          .eq('profession', profession)
          .eq('is_public', true)
          .limit(limit);

      return response.map<UserProfile>((json) => UserProfile.fromJson(json)).toList();
    } catch (e) {
      print('Mesleğe göre kullanıcılar getirme hatası: $e');
      return [];
    }
  }

  // Profil arama
  Future<List<UserProfile>> searchProfiles({
    String? query,
    String? country,
    String? profession,
    String? experienceLevel,
    List<String>? helpTopics,
    int limit = 20,
  }) async {
    try {
      final response = await _supabase
          .from('user_profiles')
          .select()
          .eq('is_public', true)
          .limit(limit);

      return response.map<UserProfile>((json) => UserProfile.fromJson(json)).toList();
    } catch (e) {
      print('Profil arama hatası: $e');
      return [];
    }
  }
}
