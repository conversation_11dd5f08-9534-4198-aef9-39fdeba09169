import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/post_model.dart';
import '../models/user_model.dart';

class PostService {
  final SupabaseClient _supabase = Supabase.instance.client;

  // Post oluştur
  Future<Post> createPost({
    required String title,
    required String content,
    required PostType type,
    List<String> tags = const [],
    List<String> images = const [],
    String? videoUrl,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // Mevcut kullanıcıyı al
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('Kullanıcı giriş yapmamış');
      }

      // Kullanıcı bilgilerini al
      final userResponse = await _supabase
          .from('users')
          .select()
          .eq('id', user.id)
          .single();

      final userData = AppUser.fromJson(userResponse);

      // Post verilerini hazırla
      final postData = {
        'user_id': user.id,
        'user_email': userData.email,
        'user_name': userData.email.split('@')[0],
        'user_avatar': '',
        'user_location': 'Türkiye → Hollanda', // TODO: Kullanıcı profilinden al
        'user_profession': 'Yazılım Geliştirici', // TODO: Kullanıcı profilinden al
        'type': type.value,
        'title': title,
        'content': content,
        'tags': tags,
        'images': images,
        'video_url': videoUrl,
        'metadata': metadata,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        'likes_count': 0,
        'comments_count': 0,
        'shares_count': 0,
      };

      // Veritabanına kaydet
      final response = await _supabase
          .from('posts')
          .insert(postData)
          .select()
          .single();

      return Post.fromJson(response);
    } catch (e) {
      print('Post oluşturma hatası: $e');
      throw Exception('Post oluşturulamadı: $e');
    }
  }

  // Feed postlarını getir
  Future<List<Post>> getFeedPosts({
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final response = await _supabase
          .from('posts')
          .select()
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      return response.map<Post>((json) => Post.fromJson(json)).toList();
    } catch (e) {
      print('Feed postları getirme hatası: $e');
      throw Exception('Postlar getirilemedi: $e');
    }
  }

  // Kullanıcının postlarını getir
  Future<List<Post>> getUserPosts(String userId) async {
    try {
      final response = await _supabase
          .from('posts')
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      return response.map<Post>((json) => Post.fromJson(json)).toList();
    } catch (e) {
      print('Kullanıcı postları getirme hatası: $e');
      throw Exception('Kullanıcı postları getirilemedi: $e');
    }
  }

  // Post beğen/beğenme
  Future<void> toggleLike(String postId) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('Kullanıcı giriş yapmamış');
      }

      // Mevcut beğeni durumunu kontrol et
      final existingLike = await _supabase
          .from('post_likes')
          .select()
          .eq('post_id', postId)
          .eq('user_id', user.id)
          .maybeSingle();

      if (existingLike != null) {
        // Beğeniyi kaldır
        await _supabase
            .from('post_likes')
            .delete()
            .eq('post_id', postId)
            .eq('user_id', user.id);

        // Post beğeni sayısını azalt
        await _supabase.rpc('decrement_post_likes', params: {
          'post_id': postId,
        });
      } else {
        // Beğeni ekle
        await _supabase.from('post_likes').insert({
          'post_id': postId,
          'user_id': user.id,
          'created_at': DateTime.now().toIso8601String(),
        });

        // Post beğeni sayısını artır
        await _supabase.rpc('increment_post_likes', params: {
          'post_id': postId,
        });
      }
    } catch (e) {
      print('Beğeni toggle hatası: $e');
      throw Exception('Beğeni işlemi başarısız: $e');
    }
  }

  // Post sil
  Future<void> deletePost(String postId) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('Kullanıcı giriş yapmamış');
      }

      await _supabase
          .from('posts')
          .delete()
          .eq('id', postId)
          .eq('user_id', user.id); // Sadece kendi postunu silebilir
    } catch (e) {
      print('Post silme hatası: $e');
      throw Exception('Post silinemedi: $e');
    }
  }

  // Post güncelle
  Future<Post> updatePost({
    required String postId,
    String? title,
    String? content,
    List<String>? tags,
    List<String>? images,
    String? videoUrl,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('Kullanıcı giriş yapmamış');
      }

      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (title != null) updateData['title'] = title;
      if (content != null) updateData['content'] = content;
      if (tags != null) updateData['tags'] = tags;
      if (images != null) updateData['images'] = images;
      if (videoUrl != null) updateData['video_url'] = videoUrl;
      if (metadata != null) updateData['metadata'] = metadata;

      final response = await _supabase
          .from('posts')
          .update(updateData)
          .eq('id', postId)
          .eq('user_id', user.id) // Sadece kendi postunu güncelleyebilir
          .select()
          .single();

      return Post.fromJson(response);
    } catch (e) {
      print('Post güncelleme hatası: $e');
      throw Exception('Post güncellenemedi: $e');
    }
  }

  // Belirli bir postu getir
  Future<Post> getPost(String postId) async {
    try {
      final response = await _supabase
          .from('posts')
          .select()
          .eq('id', postId)
          .single();

      return Post.fromJson(response);
    } catch (e) {
      print('Post getirme hatası: $e');
      throw Exception('Post getirilemedi: $e');
    }
  }

  // Trend olan postları getir
  Future<List<Post>> getTrendingPosts({int limit = 10}) async {
    try {
      final response = await _supabase
          .from('posts')
          .select()
          .order('likes_count', ascending: false)
          .order('comments_count', ascending: false)
          .limit(limit);

      return response.map<Post>((json) => Post.fromJson(json)).toList();
    } catch (e) {
      print('Trend postları getirme hatası: $e');
      throw Exception('Trend postları getirilemedi: $e');
    }
  }

  // Tag'e göre postları getir
  Future<List<Post>> getPostsByTag(String tag) async {
    try {
      final response = await _supabase
          .from('posts')
          .select()
          .contains('tags', [tag])
          .order('created_at', ascending: false);

      return response.map<Post>((json) => Post.fromJson(json)).toList();
    } catch (e) {
      print('Tag postları getirme hatası: $e');
      throw Exception('Tag postları getirilemedi: $e');
    }
  }

  // Post türüne göre postları getir
  Future<List<Post>> getPostsByType(PostType type) async {
    try {
      final response = await _supabase
          .from('posts')
          .select()
          .eq('type', type.value)
          .order('created_at', ascending: false);

      return response.map<Post>((json) => Post.fromJson(json)).toList();
    } catch (e) {
      print('Tip postları getirme hatası: $e');
      throw Exception('Tip postları getirilemedi: $e');
    }
  }
}
