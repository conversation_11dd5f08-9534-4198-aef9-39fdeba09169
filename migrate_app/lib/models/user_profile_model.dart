class UserProfile {
  final String id;
  final String userId;
  final String email;
  final String firstName;
  final String lastName;
  final String? avatar;
  final String? bio;
  
  // Göç Bilgileri
  final String fromCountry;
  final String fromCity;
  final String toCountry;
  final String toCity;
  final DateTime? migrationDate;
  final String migrationStatus; // 'planning', 'in-progress', 'completed'
  
  // Profesyonel Bilgiler
  final String profession;
  final String industry;
  final String experienceLevel; // 'junior', 'mid', 'senior', 'expert'
  final List<String> skills;
  final String? currentCompany;
  final String? currentPosition;
  
  // Networking Tercihleri
  final List<String> helpTopics; // ['vize', 'konut', 'iş', 'eğitim', 'sağlık']
  final List<String> userCategories; // ['yeni-göçmen', 'deneyimli', 'profesyonel', 'sosyal']
  final List<String> networkingGoals; // ['iş-bulma', 'arkadaş-edinme', 'yardım-etme', 'öğrenme']
  
  // İletişim Tercihleri
  final bool isOpenToMentoring;
  final bool isSeekingMentor;
  final bool isOpenToNetworking;
  final List<String> preferredLanguages; // ['tr', 'en', 'nl', 'de']
  
  // Sosyal Medya
  final String? linkedinUrl;
  final String? twitterUrl;
  final String? instagramUrl;
  final String? websiteUrl;
  
  // Sistem Bilgileri
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isVerified;
  final bool isPublic;

  UserProfile({
    required this.id,
    required this.userId,
    required this.email,
    this.firstName = '',
    this.lastName = '',
    this.avatar,
    this.bio,
    this.fromCountry = '',
    this.fromCity = '',
    this.toCountry = '',
    this.toCity = '',
    this.migrationDate,
    this.migrationStatus = 'planning',
    this.profession = '',
    this.industry = '',
    this.experienceLevel = 'mid',
    this.skills = const [],
    this.currentCompany,
    this.currentPosition,
    this.helpTopics = const [],
    this.userCategories = const [],
    this.networkingGoals = const [],
    this.isOpenToMentoring = false,
    this.isSeekingMentor = false,
    this.isOpenToNetworking = true,
    this.preferredLanguages = const ['tr'],
    this.linkedinUrl,
    this.twitterUrl,
    this.instagramUrl,
    this.websiteUrl,
    required this.createdAt,
    required this.updatedAt,
    this.isVerified = false,
    this.isPublic = true,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? '',
      email: json['email'] ?? '',
      firstName: json['first_name'] ?? '',
      lastName: json['last_name'] ?? '',
      avatar: json['avatar'],
      bio: json['bio'],
      fromCountry: json['from_country'] ?? '',
      fromCity: json['from_city'] ?? '',
      toCountry: json['to_country'] ?? '',
      toCity: json['to_city'] ?? '',
      migrationDate: json['migration_date'] != null 
          ? DateTime.parse(json['migration_date']) 
          : null,
      migrationStatus: json['migration_status'] ?? 'planning',
      profession: json['profession'] ?? '',
      industry: json['industry'] ?? '',
      experienceLevel: json['experience_level'] ?? 'mid',
      skills: List<String>.from(json['skills'] ?? []),
      currentCompany: json['current_company'],
      currentPosition: json['current_position'],
      helpTopics: List<String>.from(json['help_topics'] ?? []),
      userCategories: List<String>.from(json['user_categories'] ?? []),
      networkingGoals: List<String>.from(json['networking_goals'] ?? []),
      isOpenToMentoring: json['is_open_to_mentoring'] ?? false,
      isSeekingMentor: json['is_seeking_mentor'] ?? false,
      isOpenToNetworking: json['is_open_to_networking'] ?? true,
      preferredLanguages: List<String>.from(json['preferred_languages'] ?? ['tr']),
      linkedinUrl: json['linkedin_url'],
      twitterUrl: json['twitter_url'],
      instagramUrl: json['instagram_url'],
      websiteUrl: json['website_url'],
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
      isVerified: json['is_verified'] ?? false,
      isPublic: json['is_public'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'email': email,
      'first_name': firstName,
      'last_name': lastName,
      'avatar': avatar,
      'bio': bio,
      'from_country': fromCountry,
      'from_city': fromCity,
      'to_country': toCountry,
      'to_city': toCity,
      'migration_date': migrationDate?.toIso8601String(),
      'migration_status': migrationStatus,
      'profession': profession,
      'industry': industry,
      'experience_level': experienceLevel,
      'skills': skills,
      'current_company': currentCompany,
      'current_position': currentPosition,
      'help_topics': helpTopics,
      'user_categories': userCategories,
      'networking_goals': networkingGoals,
      'is_open_to_mentoring': isOpenToMentoring,
      'is_seeking_mentor': isSeekingMentor,
      'is_open_to_networking': isOpenToNetworking,
      'preferred_languages': preferredLanguages,
      'linkedin_url': linkedinUrl,
      'twitter_url': twitterUrl,
      'instagram_url': instagramUrl,
      'website_url': websiteUrl,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_verified': isVerified,
      'is_public': isPublic,
    };
  }

  UserProfile copyWith({
    String? id,
    String? userId,
    String? email,
    String? firstName,
    String? lastName,
    String? avatar,
    String? bio,
    String? fromCountry,
    String? fromCity,
    String? toCountry,
    String? toCity,
    DateTime? migrationDate,
    String? migrationStatus,
    String? profession,
    String? industry,
    String? experienceLevel,
    List<String>? skills,
    String? currentCompany,
    String? currentPosition,
    List<String>? helpTopics,
    List<String>? userCategories,
    List<String>? networkingGoals,
    bool? isOpenToMentoring,
    bool? isSeekingMentor,
    bool? isOpenToNetworking,
    List<String>? preferredLanguages,
    String? linkedinUrl,
    String? twitterUrl,
    String? instagramUrl,
    String? websiteUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isVerified,
    bool? isPublic,
  }) {
    return UserProfile(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      avatar: avatar ?? this.avatar,
      bio: bio ?? this.bio,
      fromCountry: fromCountry ?? this.fromCountry,
      fromCity: fromCity ?? this.fromCity,
      toCountry: toCountry ?? this.toCountry,
      toCity: toCity ?? this.toCity,
      migrationDate: migrationDate ?? this.migrationDate,
      migrationStatus: migrationStatus ?? this.migrationStatus,
      profession: profession ?? this.profession,
      industry: industry ?? this.industry,
      experienceLevel: experienceLevel ?? this.experienceLevel,
      skills: skills ?? this.skills,
      currentCompany: currentCompany ?? this.currentCompany,
      currentPosition: currentPosition ?? this.currentPosition,
      helpTopics: helpTopics ?? this.helpTopics,
      userCategories: userCategories ?? this.userCategories,
      networkingGoals: networkingGoals ?? this.networkingGoals,
      isOpenToMentoring: isOpenToMentoring ?? this.isOpenToMentoring,
      isSeekingMentor: isSeekingMentor ?? this.isSeekingMentor,
      isOpenToNetworking: isOpenToNetworking ?? this.isOpenToNetworking,
      preferredLanguages: preferredLanguages ?? this.preferredLanguages,
      linkedinUrl: linkedinUrl ?? this.linkedinUrl,
      twitterUrl: twitterUrl ?? this.twitterUrl,
      instagramUrl: instagramUrl ?? this.instagramUrl,
      websiteUrl: websiteUrl ?? this.websiteUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isVerified: isVerified ?? this.isVerified,
      isPublic: isPublic ?? this.isPublic,
    );
  }

  // Yardımcı metodlar
  String get fullName {
    if (firstName.isEmpty && lastName.isEmpty) {
      return email.split('@')[0];
    }
    return '$firstName $lastName'.trim();
  }

  String get displayName {
    if (firstName.isNotEmpty) {
      return firstName;
    }
    return email.split('@')[0];
  }

  String get migrationRoute {
    if (fromCountry.isEmpty || toCountry.isEmpty) {
      return 'Göç rotası belirtilmemiş';
    }
    return '$fromCountry → $toCountry';
  }

  String get locationInfo {
    final from = fromCity.isNotEmpty ? '$fromCity, $fromCountry' : fromCountry;
    final to = toCity.isNotEmpty ? '$toCity, $toCountry' : toCountry;
    
    if (from.isEmpty && to.isEmpty) {
      return 'Konum belirtilmemiş';
    } else if (from.isEmpty) {
      return to;
    } else if (to.isEmpty) {
      return from;
    } else {
      return '$from → $to';
    }
  }

  String get professionalTitle {
    if ((currentPosition?.isNotEmpty ?? false) && (currentCompany?.isNotEmpty ?? false)) {
      return '$currentPosition @ $currentCompany';
    } else if (currentPosition?.isNotEmpty ?? false) {
      return currentPosition!;
    } else if (profession.isNotEmpty) {
      return profession;
    } else {
      return 'Meslek belirtilmemiş';
    }
  }

  bool get isProfileComplete {
    return firstName.isNotEmpty &&
           lastName.isNotEmpty &&
           fromCountry.isNotEmpty &&
           toCountry.isNotEmpty &&
           profession.isNotEmpty &&
           helpTopics.isNotEmpty &&
           userCategories.isNotEmpty;
  }

  int get profileCompletionPercentage {
    int completed = 0;
    int total = 10;

    if (firstName.isNotEmpty) completed++;
    if (lastName.isNotEmpty) completed++;
    if (bio != null && bio!.isNotEmpty) completed++;
    if (fromCountry.isNotEmpty) completed++;
    if (toCountry.isNotEmpty) completed++;
    if (profession.isNotEmpty) completed++;
    if (skills.isNotEmpty) completed++;
    if (helpTopics.isNotEmpty) completed++;
    if (userCategories.isNotEmpty) completed++;
    if (networkingGoals.isNotEmpty) completed++;

    return ((completed / total) * 100).round();
  }
}
