class Post {
  final String id;
  final String userId;
  final String userEmail;
  final String userName;
  final String userAvatar;
  final String userLocation; // "Türk<PERSON><PERSON> → Hollanda"
  final String userProfession;
  final PostType type;
  final String title;
  final String content;
  final List<String> tags;
  final List<String> images;
  final String? videoUrl;
  final Map<String, dynamic>? metadata; // Tip-specific data
  final DateTime createdAt;
  final DateTime updatedAt;
  final int likesCount;
  final int commentsCount;
  final int sharesCount;
  final bool isLiked;
  final bool isBookmarked;

  Post({
    required this.id,
    required this.userId,
    required this.userEmail,
    required this.userName,
    this.userAvatar = '',
    this.userLocation = '',
    this.userProfession = '',
    required this.type,
    required this.title,
    required this.content,
    this.tags = const [],
    this.images = const [],
    this.videoUrl,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
    this.likesCount = 0,
    this.commentsCount = 0,
    this.sharesCount = 0,
    this.isLiked = false,
    this.isBookmarked = false,
  });

  factory Post.fromJson(Map<String, dynamic> json) {
    return Post(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? '',
      userEmail: json['user_email'] ?? '',
      userName: json['user_name'] ?? '',
      userAvatar: json['user_avatar'] ?? '',
      userLocation: json['user_location'] ?? '',
      userProfession: json['user_profession'] ?? '',
      type: PostType.fromString(json['type'] ?? 'experience'),
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      tags: List<String>.from(json['tags'] ?? []),
      images: List<String>.from(json['images'] ?? []),
      videoUrl: json['video_url'],
      metadata: json['metadata'],
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
      likesCount: json['likes_count'] ?? 0,
      commentsCount: json['comments_count'] ?? 0,
      sharesCount: json['shares_count'] ?? 0,
      isLiked: json['is_liked'] ?? false,
      isBookmarked: json['is_bookmarked'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'user_email': userEmail,
      'user_name': userName,
      'user_avatar': userAvatar,
      'user_location': userLocation,
      'user_profession': userProfession,
      'type': type.value,
      'title': title,
      'content': content,
      'tags': tags,
      'images': images,
      'video_url': videoUrl,
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'likes_count': likesCount,
      'comments_count': commentsCount,
      'shares_count': sharesCount,
      'is_liked': isLiked,
      'is_bookmarked': isBookmarked,
    };
  }

  Post copyWith({
    String? id,
    String? userId,
    String? userEmail,
    String? userName,
    String? userAvatar,
    String? userLocation,
    String? userProfession,
    PostType? type,
    String? title,
    String? content,
    List<String>? tags,
    List<String>? images,
    String? videoUrl,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? likesCount,
    int? commentsCount,
    int? sharesCount,
    bool? isLiked,
    bool? isBookmarked,
  }) {
    return Post(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userEmail: userEmail ?? this.userEmail,
      userName: userName ?? this.userName,
      userAvatar: userAvatar ?? this.userAvatar,
      userLocation: userLocation ?? this.userLocation,
      userProfession: userProfession ?? this.userProfession,
      type: type ?? this.type,
      title: title ?? this.title,
      content: content ?? this.content,
      tags: tags ?? this.tags,
      images: images ?? this.images,
      videoUrl: videoUrl ?? this.videoUrl,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      likesCount: likesCount ?? this.likesCount,
      commentsCount: commentsCount ?? this.commentsCount,
      sharesCount: sharesCount ?? this.sharesCount,
      isLiked: isLiked ?? this.isLiked,
      isBookmarked: isBookmarked ?? this.isBookmarked,
    );
  }
}

enum PostType {
  experience('experience', '📝', 'Deneyim'),
  question('question', '❓', 'Soru'),
  event('event', '📅', 'Etkinlik'),
  job('job', '💼', 'İş İlanı'),
  success('success', '🎉', 'Başarı');

  const PostType(this.value, this.emoji, this.label);

  final String value;
  final String emoji;
  final String label;

  static PostType fromString(String value) {
    switch (value) {
      case 'experience':
        return PostType.experience;
      case 'question':
        return PostType.question;
      case 'event':
        return PostType.event;
      case 'job':
        return PostType.job;
      case 'success':
        return PostType.success;
      default:
        return PostType.experience;
    }
  }
}

// Tip-specific metadata structures
class ExperienceMetadata {
  final String country;
  final String city;
  final List<String> topics; // ['konut', 'vize', 'iş', 'eğitim']
  final int rating; // 1-5 yıldız

  ExperienceMetadata({
    required this.country,
    required this.city,
    required this.topics,
    required this.rating,
  });

  factory ExperienceMetadata.fromJson(Map<String, dynamic> json) {
    return ExperienceMetadata(
      country: json['country'] ?? '',
      city: json['city'] ?? '',
      topics: List<String>.from(json['topics'] ?? []),
      rating: json['rating'] ?? 5,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'country': country,
      'city': city,
      'topics': topics,
      'rating': rating,
    };
  }
}

class QuestionMetadata {
  final String category; // 'vize', 'konut', 'iş', 'eğitim', 'sağlık'
  final String urgency; // 'düşük', 'orta', 'yüksek'
  final bool isAnswered;

  QuestionMetadata({
    required this.category,
    required this.urgency,
    this.isAnswered = false,
  });

  factory QuestionMetadata.fromJson(Map<String, dynamic> json) {
    return QuestionMetadata(
      category: json['category'] ?? '',
      urgency: json['urgency'] ?? 'orta',
      isAnswered: json['is_answered'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'category': category,
      'urgency': urgency,
      'is_answered': isAnswered,
    };
  }
}

class EventMetadata {
  final DateTime eventDate;
  final String location;
  final String address;
  final int maxParticipants;
  final int currentParticipants;
  final bool isOnline;
  final String? meetingLink;
  final double? price;

  EventMetadata({
    required this.eventDate,
    required this.location,
    required this.address,
    this.maxParticipants = 0,
    this.currentParticipants = 0,
    this.isOnline = false,
    this.meetingLink,
    this.price,
  });

  factory EventMetadata.fromJson(Map<String, dynamic> json) {
    return EventMetadata(
      eventDate: DateTime.parse(json['event_date'] ?? DateTime.now().toIso8601String()),
      location: json['location'] ?? '',
      address: json['address'] ?? '',
      maxParticipants: json['max_participants'] ?? 0,
      currentParticipants: json['current_participants'] ?? 0,
      isOnline: json['is_online'] ?? false,
      meetingLink: json['meeting_link'],
      price: json['price']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'event_date': eventDate.toIso8601String(),
      'location': location,
      'address': address,
      'max_participants': maxParticipants,
      'current_participants': currentParticipants,
      'is_online': isOnline,
      'meeting_link': meetingLink,
      'price': price,
    };
  }
}

class JobMetadata {
  final String company;
  final String position;
  final String jobType; // 'tam-zamanlı', 'yarı-zamanlı', 'freelance'
  final String experienceLevel; // 'junior', 'mid', 'senior'
  final String salaryRange;
  final List<String> requirements;
  final String applicationUrl;
  final DateTime? deadline;

  JobMetadata({
    required this.company,
    required this.position,
    required this.jobType,
    required this.experienceLevel,
    required this.salaryRange,
    required this.requirements,
    required this.applicationUrl,
    this.deadline,
  });

  factory JobMetadata.fromJson(Map<String, dynamic> json) {
    return JobMetadata(
      company: json['company'] ?? '',
      position: json['position'] ?? '',
      jobType: json['job_type'] ?? '',
      experienceLevel: json['experience_level'] ?? '',
      salaryRange: json['salary_range'] ?? '',
      requirements: List<String>.from(json['requirements'] ?? []),
      applicationUrl: json['application_url'] ?? '',
      deadline: json['deadline'] != null ? DateTime.parse(json['deadline']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'company': company,
      'position': position,
      'job_type': jobType,
      'experience_level': experienceLevel,
      'salary_range': salaryRange,
      'requirements': requirements,
      'application_url': applicationUrl,
      'deadline': deadline?.toIso8601String(),
    };
  }
}

class SuccessMetadata {
  final String achievement; // 'iş-bulma', 'vize-alma', 'ev-bulma', 'mezuniyet'
  final String timeline; // '3 ay', '6 ay', '1 yıl'
  final List<String> tips;

  SuccessMetadata({
    required this.achievement,
    required this.timeline,
    required this.tips,
  });

  factory SuccessMetadata.fromJson(Map<String, dynamic> json) {
    return SuccessMetadata(
      achievement: json['achievement'] ?? '',
      timeline: json['timeline'] ?? '',
      tips: List<String>.from(json['tips'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'achievement': achievement,
      'timeline': timeline,
      'tips': tips,
    };
  }
}
