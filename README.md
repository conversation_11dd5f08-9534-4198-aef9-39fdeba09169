# Migrate App

Migrate App, <PERSON><PERSON><PERSON><PERSON><PERSON>'den yurt dış<PERSON>na g<PERSON><PERSON> eden bi<PERSON>, özellikle Avrupa başta olmak ü<PERSON>e, gittikleri ülkelerdeki diğer Türk göçmenlerle tanışmalarını, et<PERSON><PERSON><PERSON><PERSON> katılmalarını, iş fırsatlarını paylaşmalarını ve sosyal bağ kurmalarını sağlayan dijital bir topluluk platformudur.

## Özellikler

- Kullanıcı kaydı ve girişi
- Şehir bazlı etkinlik listeleme
- Etkinlik oluşturma (admin kullanıcılar için)
- Etkinliklere katılma ve ayrılma
- Etkinlik detaylarını görüntüleme
- Türkçe dil desteği

## Kurulum

### Gereksinimler

- Flutter SDK (3.7.0 veya üzeri)
- Dart SDK (3.0.0 veya üzeri)
- Supabase hesabı

### Adımlar

1. <PERSON><PERSON>yi k<PERSON>:
   ```
   git clone https://github.com/kullanici/migrate_app.git
   cd migrate_app
   ```

2. Bağımlılıkları yükleyin:
   ```
   flutter pub get
   ```

3. Supabase Kurulumu:
   - Supabase'de yeni bir proje oluşturun
   - Aşağıdaki tabloları oluşturun:

     **Table: `events`**
     - id (uuid, PK)
     - title (text)
     - description (text)
     - city (text)
     - date (timestamp)
     - image_url (text)
     - participants (uuid[])

     **Table: `admins`**
     - user_id (uuid, PK)

     **Table: `users`**
     - id (uuid, PK)
     - email (text)
     - created_at (timestamp)
     - country (text)
     - city (text)

   - Supabase Storage'da `event_images` adında bir bucket oluşturun ve public erişime açın

4. Supabase bilgilerinizi ekleyin:
   - `lib/constants/supabase_constants.dart` dosyasını açın
   - `supabaseUrl` ve `supabaseAnonKey` değerlerini kendi Supabase projenizden alın ve güncelleyin

5. Uygulamayı çalıştırın:
   ```
   flutter run
   ```

## Kullanım

### Kullanıcı Kaydı
- Uygulama ilk açıldığında kayıt ekranı görüntülenir
- E-posta, şifre ve şehir bilgilerinizi girerek kayıt olun

### Etkinliklere Katılma
- Ana ekranda şehrinize ait etkinlikler listelenir
- Etkinlik kartındaki "Katıl" butonuna tıklayarak etkinliğe katılabilirsiniz
- "Ayrıl" butonuna tıklayarak etkinlikten ayrılabilirsiniz

### Admin Kullanıcılar İçin
- Admin kullanıcılar sağ alt köşedeki "+" butonuna tıklayarak yeni etkinlik oluşturabilir
- Etkinlik başlığı, açıklaması, şehri, tarihi ve görseli ekleyebilirsiniz
- Admin kullanıcılar etkinlikleri silebilir

## Gelecek Özellikler

- İş İlanları bölümü
- Konaklama / eşya paylaşım alanı
- Forum tarzı açık yorum alanları
- Diğer ülkeler için filtreleme
- E-posta bildirim sistemi

## Katkıda Bulunma

Projeye katkıda bulunmak için lütfen bir pull request açın veya önerilerinizi issues bölümünde paylaşın.

## Lisans

Bu proje MIT lisansı altında lisanslanmıştır - detaylar için [LICENSE](LICENSE) dosyasına bakın.
